import { Navigate, useLocation } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { getAllServices } from "../../apis/settings";

const ProtectedServiceRoute = ({ children }) => {
  const location = useLocation();

  const { data: servicesData, isLoading } = useQuery({
    queryKey: ["getallservices"],
    queryFn: getAllServices
  });

  if (isLoading) return null;

  // Find the service for this route
  let service = null;
  if (servicesData?.results) {
    // Create a mapping of normalized paths to services
    const pathToService = servicesData.results.reduce((acc, service) => {
      // Convert service slug to URL-friendly format
      const normalizedSlug = service.slug.replace(/\s/g, "-").toLowerCase();
      acc[normalizedSlug] = service;
      return acc;
    }, {});

    // Get the last part of the current path
    const currentPath = location.pathname.split('/').pop();
    
    // Find the service using the normalized path
    service = pathToService[currentPath];
  }

  console.log("service", service)

  // If service requires verification, redirect
  if (service?.verification_required) {
    const normalizedSlug = service.slug.replace(/\s/g, "-");
    return (
      <Navigate
        to={`/dashboard/services-verification/${normalizedSlug}`}
        state={{ from: location }}
        replace
      />
    );
  }

  return children;
};

export default ProtectedServiceRoute;
