"use client"

import React, { useEffect, useState } from 'react'
import MySwiper from '../components/MySwiper'
import { OnboardingLogo } from '../utils/Utils';
import { Link, Outlet } from 'react-router-dom';

export default function AuthLayout({ children }) {

  const images = [
    "/images/hand.png",
    "/images/hand.png",
    "/images/hand.png",
  ]

  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [images.length]);

  const handleIndicatorClick = (index) => {
    setActiveIndex(index);
  };

  return (
    <div className='bg-text_auth min-h-screen grid grid-cols-5 no-scrollbar overflow-auto'>
      <div className='relative min-h-screen col-span-2 bg-auth_bg text-text_auth overflow-y-hidden hidden md:flex flex-col items-center'>
        <div className='my-10'>
          <div className='text-center mb-4 font-[600] text-[28px]'>Digital Convenience</div>
          <div className='text-center mb-4 font-[400] md:px-10 md:text-[16px]'>
            Bringing together an array of essential services and
            functionalities in one seamlessly integrated platform.
          </div>
          <div className='flex items-center justify-center gap-2'>
            {images?.map((_, index) => (
              <div key={index} onClick={() => handleIndicatorClick(index)} className={`${index === activeIndex ? "w-8 bg-white" : "w-4 bg-gray-400"} cursor-pointer h-1 rounded-full`}></div>
            ))}
          </div>
        </div>
        <div className='flex px-10 absolute bottom-0'>
          <MySwiper path={images[activeIndex]} />
        </div>
      </div>
      <section className='no-scrollbar p-4 md:p-0 min-h-screen col-span-5 md:col-span-3 bg-text_auth text-general_gray_text overflow-hidden flex flex-col justify-center items-center'>

        <div className='relative h-[30px] w-[140px] my-4'>
          <Link to={"https://www.kompatapp.com/"}>
            <OnboardingLogo path={"/images/logo/logoblue.png"} />
          </Link>
        </div>

        {/* {children} */}
        <Outlet />
      </section>
    </div>
  )
} 