import { useLayoutEffect, useState } from "react";
import DashboardNav from "../components/dashboardcom/DashboardNav";
import Sidebar from "../components/dashboardcom/Sidebar";
import { Outlet, useLocation } from "react-router-dom";
import { MSidebar } from "../components/dashboardcom/MSidebar";
import { useCallModal } from "../hooks/useCallModal";
import Verifyphone from "../pages/auth/login/verifyphonenumber/Verifyphone";
import { getAccount } from "../../apis/account";
import { useQuery } from "@tanstack/react-query";
import { CiMenuFries } from "react-icons/ci";
import TawkMessengerReact from '@tawk.to/tawk-messenger-react';




export default function DashboardLayout({ children }) {

  const { pathname } = useLocation()
  const showSidebar = pathname.startsWith("/ecommerce")

  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  const { isOpenModal, openModal } = useCallModal();

  const { data: accountData } = useQuery({
    queryFn: getAccount,
    queryKey: ["getaccount"]
  });

  useLayoutEffect(() => {
    if (accountData &&
      (accountData?.verification?.phone_verification_status !== 'verified')) {
      openModal();
    }
  }, [accountData, openModal]);

  return (

    <>
      <section>
        <div className="flex overflow-x-hidden flex-col"> {/* Added flex-col */}
          <DashboardNav />

          <div className="flex flex-1"> {/* Added flex container */}
            {isSidebarOpen && (
              <div className="fixed inset-0 bg-black opacity-50 z-10" onClick={closeSidebar}></div>
            )}

            {showSidebar && (
              <div className={`fixed inset-y-0 left-0 z-20 transition-transform transform ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'} .md:relative md:translate-x-0 md:z-0`}>
                <Sidebar closeSidebar={closeSidebar} />
              </div>
            )}

            <div className="w-full ml-0 .md:ml-[265px]">
              {!isSidebarOpen && (
                <button className="md:hidden absolute top-6 right-2 z-30 text-gray-500 p-2" onClick={toggleSidebar}>
                  <CiMenuFries size={20} />
                </button>
              )}

              <MSidebar isOpen={isSidebarOpen} closeMenu={closeSidebar} />

              <section className="min-h-screen p-2 md:p-6 md:px-16 bg-[#FCFCFC]">
                <Outlet />
              </section>
            </div>
          </div>
        </div>

        {isOpenModal && <Verifyphone />}
      </section>

      <TawkMessengerReact
        propertyId="68384cd018e960190e2165bc"
        widgetId="1isduo45k" />
    </>

  )
}
