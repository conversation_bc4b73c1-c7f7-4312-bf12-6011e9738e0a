import React from 'react'

export const SignInWithGoogle = () => {

  const redirectToGoogleSignIn = () => {
    const clientId = '************-089un00stbasetfusb34nfh1ii086hs1.apps.googleusercontent.com'; // Replace with your client ID
    const redirectUri = 'https://www.kompatapp.com/google-auth-redirect'; // Replace with your redirect URI
    const scope = 'profile email';
    const responseType = 'code';
    const accessType = 'offline'; // Optional, if you want a refresh token
    const googleAuthUrl = `https://accounts.google.com/o/oauth2/auth?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&response_type=${responseType}&access_type=${accessType}`;

    // Redirect the user to the Google sign-in page
    window.location.href = googleAuthUrl;
    
};
  return {
    redirectToGoogleSignIn
  }
}