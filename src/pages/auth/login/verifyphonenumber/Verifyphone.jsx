import React, { useState } from 'react'
import Modal from '../../../../components/modal/Modal'
import Button from '../../../../components/button/button'
import { useMutation } from '@tanstack/react-query'
import toast from 'react-hot-toast'
import { <PERSON><PERSON><PERSON>oader } from '../../../../utils/ButtonLoader'
import { BiBadgeCheck } from 'react-icons/bi'
import { PhoneInput } from 'react-international-phone'
import 'react-international-phone/style.css'
import { useCallModal } from '../../../../hooks/useCallModal'
import { resendVerificationCodeApi } from '../../../../../apis/account'
import { useNavigate } from 'react-router-dom'

const Verifyphone = () => {
  const [phoneNumber, setPhoneNumber] = useState("")
  const [country, setCountry] = useState("ng")
  const { closeModal } = useCallModal()
  const navigate = useNavigate()

  const { mutate: verifyPhoneNumberMutation, isPending } = useMutation({
    mutationFn: resendVerificationCodeApi,
    onSuccess: (data) => {
      toast.success(`${data?.detail}`, { duration: 5000 });
      
      // Update user data in localStorage with the new phone number
      const userData = JSON.parse(localStorage.getItem("Kompat_userData"));
      if (userData) {
        userData.userData.phone_number = phoneNumber;
        localStorage.setItem("Kompat_userData", JSON.stringify(userData));
      }
      
      // Redirect to dashboard or welcome page
      const storageData = localStorage.getItem('Kompat_userData');
      if (storageData?.userData?.is_transaction_pin_set) {
        navigate("/welcome");
      } else {
        navigate("/verify");
      }
      
      closeModal();
    },
    onError: (error) => {
      toast.error(`${error?.detail}`, { duration: 5000 });
    }
  });

  const handlePhoneChange = (phone, countryData) => {
    setPhoneNumber(phone);
    setCountry(countryData.iso2);
  };

  const handleVerifyPhone = (e) => {
    e.preventDefault();
    if (!phoneNumber) {
      toast.error("Please enter a valid phone number");
      return;
    }
    
    verifyPhoneNumberMutation({
      phone_number: phoneNumber,
      country_code: country
    });
  };

  return (
    <div>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={false}
        title="Verify Phone Number"
      >
        <form onSubmit={handleVerifyPhone} className='overflow-hidden'>
          <div className='flex flex-col mx-auto justify-center w-full my-5'>
            <div className='text-sm text-start font-[400] py-2'>Enter Phone Number</div>
            <PhoneInput
              defaultCountry="ng"
              value={phoneNumber}
              onChange={handlePhoneChange}
              inputStyle={{ width: "100%", padding: "20px 10px" }}
              placeholder='Enter phone number'
              
            />

            <div className='mt-10'>
              <Button
                type='submit'
                disabled={!phoneNumber || isPending}
                className={`p-3 px-6 btn flex items-center justify-center gap-2 font-md text-xs ${!phoneNumber && 'btn_opacity'}`}
              >
                {isPending ? (
                  <ButtonLoader />
                ) : (
                  <>
                    <span>Proceed</span>
                    <BiBadgeCheck className='text-lg' />
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </Modal>
    </div>
  )
}

export default Verifyphone
