import { <PERSON>, useNavigate } from 'react-router-dom';
import React, { useState } from 'react';
import { AiOutlineEyeInvisible, AiOutlineEye } from "react-icons/ai";
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { loginApi, resetPassword } from '../../../../apis/account';
import Button from '../../../components/button/button';
import { ButtonLoader } from '../../../utils/ButtonLoader';
import Input from '../../../components/input/input';
import { TextUnder } from '../../../utils/Utils';
import { PhoneInput } from 'react-international-phone';
import 'react-international-phone/style.css';
import { useGoogleLogin } from '@react-oauth/google';
import loginWithGoogle from '../../../hooks/loginWithGoogle';
import { PiUserSwitchDuotone } from "react-icons/pi";




const Login = () => {

  const [togglePassword, setTogglePassword] = useState(false)
  const [loginWith, setLoginWith] = useState(true)
  const [rememberMe, setRememberMe] = useState(false)
  const [switchAccount, setSwitchAccount] = useState(false)

  const getExistingUser = JSON.parse(
    localStorage.getItem("Kompat_userData")
  )

  const existingEmail = getExistingUser?.userData?.email;
  const Fullname = getExistingUser?.userData?.full_name

  const navigate = useNavigate();

  const onGoogleLoginSuccess = (accessToken) => {
    setFormData((prevData) => ({
      ...prevData,
      google_id_token: accessToken,
    }));
    loginMutate({ ...formData, google_id_token: accessToken, login_with: "google", });
  };

  const { responseMessage, errorMessage, userDataFromGoogle } = loginWithGoogle(onGoogleLoginSuccess);

  const signIn = useGoogleLogin({
    onSuccess: responseMessage,
    onError: errorMessage,
  })


  console.log("expected", userDataFromGoogle)

  const handleTogglePassword = () => setTogglePassword(!togglePassword)

  const [formData, setFormData] = useState({
    source: "web",
    login_with: "email",
    country: "",
    password: "",
    user: existingEmail || "",
    google_id_token: ""
  })


  const handleToggleLoginWith = () => {
    setLoginWith(!loginWith);
    setFormData((prevData) => ({
      ...prevData,
      login_with: loginWith ? "phone" : "email",
      user: ""
    }));
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value })
  }

  const { mutate: loginMutate, isPending } = useMutation({
    mutationFn: loginApi,

    onSuccess: (data) => {
      console.log(data)

      const userData = data?.data
      const userAuthToken = data?.token

      const kompatKey = 'Kompat_userData'
      const token = 'token'

      if (rememberMe) {
        const expires = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
        localStorage.setItem(kompatKey, JSON.stringify({ userData, expires }))
        localStorage.setItem(token, JSON.stringify({ userAuthToken }))
      } else {
        localStorage.setItem(kompatKey, JSON.stringify({ userData }))
        localStorage.setItem(token, JSON.stringify({ userAuthToken }))
      }

      // Only redirect if phone number exists
      // Check if there's a lastPath saved
      const lastPath = localStorage.getItem('lastPath') || '/welcome';
      localStorage.removeItem('lastPath'); // Clear it after use

      // Redirect to last path or dashboard
      window.location.href = lastPath;

      toast.success(`${data?.detail}`, {
        duration: 5000,
      })
    },
    onError: (error) => {
      console.log(error?.detail)
      toast.error(`${error?.detail}`, {
        duration: 5000,
      })

      if (error?.code === "set_password") {
        try {
          // Prepare data for reset password
          const resetData = {
            reset_type: "password",
            verify_with: formData.login_with,
            user: formData.user,
            country: country,
          };
          // Store request details for the createpassword page
          localStorage.setItem("requestDetail", JSON.stringify(resetData));

          // Call resetPassword in the background
          resetPassword(resetData)
            .then(response => {
              toast.success(`${response?.detail || "Verification code sent successfully"}`, {
                duration: 10000,
              }
              );
              // Navigate to createpassword page
              return navigate("/createpassword");
            })
            .catch(err => {
              console.error("Error sending verification code:", err);
              toast.error(`${err?.detail || "Failed to send verification code"}`);
              // Still navigate to createpassword page even if there's an error
              return navigate("/createpassword");
            });
        } catch (error) {
          console.error("Error navigating to createpassword:", error);
        }
      }

    }
  })

  const handleLogin = (e) => {
    e.preventDefault(),
      loginMutate(formData)
  }

  const [phone, setPhone] = useState('');
  const [country, setCountry] = useState('');

  const handlePhoneChange = (phone, country) => {
    setPhone(phone);
    setCountry(country.iso2);

    setFormData({ ...formData, user: phone, country: country.country.iso2 });
  };

  const check = !formData?.password || !formData?.user || isPending


  return (
    <>
      <TextUnder
        header={
          <>
            <div className='text-center'>{Fullname && !switchAccount ? `Welcome Back, ${Fullname}.` : "Sign In"}</div>
            {Fullname && !switchAccount && (
              <div
                className="flex items-center justify-center text-xs text-auth_bg mt-2"
              >
                <div className='cursor-pointer transition-all duration-700 hover:bg-auth_bg/10 flex items-center gap-1 font-semibold p-1 px-4 rounded-md'
                  onClick={() => setSwitchAccount(true)}
                >
                  <PiUserSwitchDuotone size={20} />
                  <span>Switch Account</span>
                </div>
              </div>
            )}
          </>
        }
        content={!Fullname || switchAccount ? `Login to your account` : ``}
      />

      <form onSubmit={handleLogin} className='w-full md:w-[60%]'>
        {(!Fullname || switchAccount) && (
          <React.Fragment>
            {loginWith ?
              <div className='flex flex-col my-4'>
                <label htmlFor="email_address" className='mb-1 font-[400] text-[14px] text-general_text'>Enter Email Address</label>
                <Input value={formData?.user} name='user' onChange={handleChange} type="email" id='email_address' placeholder='Enter Your Email Address' className='p-3' />
              </div>
              :

              <div className='flex flex-col my-5'>
                <label htmlFor="phone_number" className='mb-1 font-[400] text-[14px] text-general_text'>Phone Number <span className='text-text_orange'>*</span></label>
                <PhoneInput
                  defaultCountry="ng"
                  value={phone}
                  onChange={handlePhoneChange}
                  id="phone_number"
                  inputStyle={{ width: "100%", padding: "20px 10px" }}
                  placeholder='Enter phone number'
                />
              </div>

              // <div className='flex flex-col my-4'>
              //   <label htmlFor="phone_number" className='mb-1 font-[400] text-[14px] text-general_text'>Enter Phone Number</label>
              //   <Input value={formData?.user} name='user' onChange={handleChange} type="tel" maxLength={11} id='phone_number' placeholder='Enter Your Phone Number' className='p-3' />
              // </div>
            }

            <div className='flex items-center justify-end'>
              <div className='font-[500] text-[13px] text-auth_bg cursor-pointer' onClick={handleToggleLoginWith}>{loginWith ? "Login with Phone Number" : "Login with Email"}</div>
            </div>

          </React.Fragment>
        )}

        <div className='flex flex-col my-5'>
          <label htmlFor="password" className='mb-1 font-[400] text-[14px] text-general_text'>Enter Password</label>
          <div className='flex items-center border rounded-[8px] pr-2'>
            <Input value={formData?.password} name='password' onChange={handleChange} type={togglePassword ? "text" : "password"} id='password' placeholder='Enter your Password' className='p-3 border-none' />
            <div onClick={handleTogglePassword} className=' cursor-pointer'>
              {togglePassword ?
                <AiOutlineEye size={20} />
                :
                <AiOutlineEyeInvisible size={20} />
              }
            </div>
          </div>
        </div>

        <div className='flex items-center justify-between mb-4'>

          <div className='flex items-center'>
            <input type="checkbox" id="remember" name="remember" checked={rememberMe} onChange={(e) => setRememberMe(e.target.checked)} />
            <label htmlFor="remember" className="ms-2 font-[400] text-[13px] text-black">Remember for 30 days</label>
          </div>

          <Link to="/forgotpassword" className='font-[400] text-[14px] text-general_gray_text cursor-pointer'>Forgot Password?</Link>
        </div>

        <div className='py-2'>
          <Button disabled={check} type='submit' className={`p-3 px-12 text-sm ${check ? 'btn_opacity' : ''}`}>
            {isPending ?
              <ButtonLoader />
              :
              <span>Login</span>
            }
          </Button>
        </div>

        <div className='py-2'>
          {/* <GoogleLogin onSuccess={responseMessage} onError={errorMessage}/> */}
          <div onClick={() => signIn()} className='cursor-pointer bg-text_auth w-full py-3 rounded-[8px] text-auth_bg border-border_color border font-[500] text-[14px] flex items-center justify-center'> <span><img src="/images/google_icon.png" className='mr-2' width={20} height={20} alt='img' /></span> Login with Google</div>
        </div>

        <div className='flex items-center text-general_text justify-center text-[14px] mt-4'>
          <span className='mr-2'>Don’t have an account?</span>
          <Link to="/signup" className=' text-auth_bg font-[500]'>Sign Up</Link>
        </div>

      </form>
      <div className='text-[12px] mt-4'>Your transactions are secure, always confirm Account Numbers, Phone Numbers and Bill Payment Numbers before making transactions.</div>

    </>
  );
};

export default Login;