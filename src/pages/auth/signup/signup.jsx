import { Link, useNavigate } from 'react-router-dom';
import React, { useState } from 'react';
import { AiOutlineEyeInvisible, AiOutlineEye } from "react-icons/ai";
import { useMutation } from '@tanstack/react-query';
import { kompatCreateAccountApi, loginApi } from '../../../../apis/account';
import { PhoneInput } from 'react-international-phone';
import 'react-international-phone/style.css';
import toast from 'react-hot-toast';
import Button from '../../../components/button/button';
import { ButtonLoader } from '../../../utils/ButtonLoader';
import Input from '../../../components/input/input';
import { TextUnder } from '../../../utils/Utils';
import { HiMiniCheckCircle } from 'react-icons/hi2';
import { GoCircle } from 'react-icons/go';
import { useGoogleLogin } from '@react-oauth/google';
import loginWithGoogle from '../../../hooks/loginWithGoogle';

const SignUp = () => {
  const [togglePassword, setTogglePassword] = useState(false);
  const handleTogglePassword = () => {
    setTogglePassword(!togglePassword);
  };

  const navigate = useNavigate()

  const [formData, setFormData] = useState({
    source: "web",
    full_name: "",
    email: "",
    phone_number: "",
    password: "",
    country: "",
    whatsapp_phone_number: "" // This will be set when phone number is entered since isWhatsApp is true
  });

  const [passwordRequirements, setPasswordRequirements] = useState({
    length: false,
    number: false,
    lowerUpperCase: false,
    specialChar: false,
  });

  const validatePassword = (password) => {
    const requirements = {
      length: password.length > 8,
      number: /\d/.test(password),
      lowerUpperCase: /[a-z]/.test(password) && /[A-Z]/.test(password),
      specialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };
    setPasswordRequirements(requirements);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name === 'full_name') {
      const isValidFullName = /^[A-Za-z\s]*$/.test(value);
      if (!isValidFullName) return;
    }
    setFormData({ ...formData, [name]: value });

    if (name === 'password') {
      validatePassword(value);
    }

  };

  const { mutate, isPending, error } = useMutation({
    mutationFn: kompatCreateAccountApi,
    onSuccess: (data) => {
      console.log("data", data);

      const userData = data?.data
      const userAuthToken = data?.token

      const kompatKey = 'Kompat_userData'
      const token = 'token'

      localStorage.setItem(kompatKey, JSON.stringify({ userData }))
      localStorage.setItem(token, JSON.stringify({ userAuthToken }))

      toast.success(`${data?.detail}`, {
        duration: 5000,
      })

      navigate("/verify")

    },
    onError: (error) => {
      console.log("error", error);
      toast.error(`${error?.detail}`, {
        duration: 5000,
      })
    },
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    mutate(formData);
  };

  const [phone, setPhone] = useState('');
  const [country, setCountry] = useState('');
  const [isWhatsApp, setIsWhatsApp] = useState(true); // Change the initial state to true


  const handlePhoneChange = (phone, country) => {
    setPhone(phone);
    setCountry(country.iso2);
    // Since isWhatsApp is true by default, always set both phone_number and whatsapp_phone_number
    setFormData({ 
      ...formData, 
      phone_number: phone, 
      country: country.country.iso2,
      whatsapp_phone_number: phone // Set WhatsApp number by default
    });
  };

  const handleWhatsAppChange = (e) => {
    const checked = e.target.checked;
    setIsWhatsApp(checked);

    if (checked) {
      setFormData(prev => ({ ...prev, whatsapp_phone_number: formData.phone_number }));
    } else {
      setFormData(prev => ({ ...prev, whatsapp_phone_number: "" }));
    }
  };

  const check = !formData?.password || !formData?.full_name || !formData?.phone_number || !formData?.email || isPending

  const { mutate: loginMutate, isPending:isLogginIn } = useMutation({
    mutationFn: loginApi,
    onSuccess: (data) => {
      console.log("Google login success:", data);
      
      const userData = data?.data;
      const userAuthToken = data?.token;

      const kompatKey = 'Kompat_userData';
      const token = 'token';

      localStorage.setItem(kompatKey, JSON.stringify({ userData }));
      localStorage.setItem(token, JSON.stringify({ userAuthToken }));

      // Check if there's a lastPath saved
      const lastPath = localStorage.getItem('lastPath') || '/welcome';
      localStorage.removeItem('lastPath'); // Clear it after use

      // Redirect to last path or dashboard
      window.location.href = lastPath;

      toast.success(`${data?.detail}`, {
        duration: 5000,
      });
    },
    onError: (error) => {
      console.log("Google login error:", error);
      toast.error(`${error?.detail}`, {
        duration: 5000,
      });
    }
  });

  const onGoogleSignUpSuccess = (accessToken) => {
    loginMutate({ 
      source: "web",
      login_with: "google",
      google_id_token: accessToken 
    });
  };

  const { responseMessage, errorMessage, userDataFromGoogle } = loginWithGoogle(onGoogleSignUpSuccess);

  const signIn = useGoogleLogin({
    onSuccess: responseMessage,
    onError: errorMessage,
  });

  return (
    <>
      {/* <div className='text-general_gray_text text-[14px]'>Step 2 of 4</div> */}

      <TextUnder header={"Personal Information"} content={"Open an account Instantly by filling the form below"} />

      <form onSubmit={handleSubmit} className='w-full md:w-[60%]'>
        <div className='flex flex-col my-5'>
          <label htmlFor="full_name" className='mb-1 font-[400] text-[14px] text-general_text'>Full Name <span className='text-text_orange'>*</span></label>
          <Input onChange={handleChange} value={formData.full_name} name='full_name' type="text" id='full_name' placeholder='Enter your full name' className='p-3' />
        </div>

        <div className='flex flex-col my-5'>
          <label htmlFor="email_address" className='mb-1 font-[400] text-[14px] text-general_text'>Email Address <span className='text-text_orange'>*</span></label>
          <Input onChange={handleChange} value={formData.email} name='email' type="email" id='email_address' placeholder='Enter your email address' className='p-3' />
        </div>

        <div className='flex flex-col my-5 mb-2'>
          <label htmlFor="phone_number" className='mb-1 font-[400] text-[14px] text-general_text'>Phone Number <span className='text-text_orange'>*</span></label>
          <PhoneInput
            defaultCountry="ng"
            value={phone}
            onChange={handlePhoneChange}
            id="phone_number"
            inputStyle={{ width: "100%", padding: "20px 10px" }}
            placeholder='Enter phone number'
          />
        </div>

        <div className='flex items-center'>
          <input 
            type="checkbox" 
            id="whatsapptick" 
            name="whatsapptick" 
            onChange={handleWhatsAppChange} 
            checked={isWhatsApp} // Add this to make it checked by default
          />
          <label htmlFor="whatsapptick" className="ms-2 font-[400] text-[13px]">
            This is my Whatsapp Number
          </label>
        </div>

        <div className='flex flex-col my-5'>
          <label htmlFor="password" className='mb-1 font-[400] text-[14px] text-general_text'>Create Password <span className='text-text_orange'>*</span></label>
          <div className='flex items-center border rounded-[8px] pr-2'>
            <Input type={togglePassword ? "password" : "text"} id='password' placeholder='Enter your password' className={"border-none p-3"} onChange={handleChange} value={formData.password} name='password' />
            <div onClick={handleTogglePassword} className='cursor-pointer'>
              {togglePassword ?
                <AiOutlineEye size={20} />
                :
                <AiOutlineEyeInvisible size={20} />
              }
            </div>
          </div>
        </div>

        <div>
          <div className='flex items-center my-2'>
            <div>
              {passwordRequirements.length ? (
                <HiMiniCheckCircle className="text-auth_bg" />
              ) : (
                <GoCircle />
              )}
            </div>
            <label htmlFor="check1" className={`ms-2 font-[400] text-[13px] ${passwordRequirements.length && 'text-green-600'}`}>Password must be more than 8 characters with at least 1 number</label>
          </div>
          <div className='flex items-center my-2'>
            <div>
              {passwordRequirements.lowerUpperCase ? (
                <HiMiniCheckCircle className="text-auth_bg" />
              ) : (
                <GoCircle />
              )}
            </div>
            <label htmlFor="check2" className={`ms-2 font-[400] text-[13px] ${passwordRequirements.lowerUpperCase && 'text-green-600'}`}>Password must contain both lower and upper case</label>
          </div>
          <div className='flex items-center my-2'>
            <div>
              {passwordRequirements.specialChar ? (
                <HiMiniCheckCircle className="text-auth_bg" />
              ) : (
                <GoCircle/>
              )}
            </div>
            <label htmlFor="check3" className={`ms-2 font-[400] text-[13px] ${passwordRequirements.specialChar && 'text-green-600'}`}>Password must contain a special character; e.g ()!”£$%&*</label>
          </div>
        </div>

        <div className='py-6'>
          <Button disabled={check} type='submit' className={`p-3 px-12 text-sm ${check ? 'btn_opacity' : ''}`}>
            {isPending ?
              <ButtonLoader />
              :
              <span>Proceed</span>
            }
          </Button>
        </div>

        <div className='pb-2'>
          <div 
            onClick={() => signIn()} 
            className='cursor-pointer bg-text_auth w-full py-3 rounded-[8px] text-auth_bg border-border_color border font-[500] text-[14px] flex items-center justify-center'
          > 
          {isLogginIn ? (
            <ButtonLoader />
          ) : (
          <div className='flex items-center'>
            <span>
              <img src="/images/google_icon.png" className='mr-2' width={20} height={20} alt='img' />
            </span> 
            Sign in with Google
          </div>
          )}
          </div>
        </div>

        <div className='flex items-center text-general_text justify-center'>
          <Link to="/">Back to Login</Link>
        </div>
      </form>
      <div className='text-[12px] mt-4'>Your transactions are secure, always confirm Account Numbers, Phone Numbers and Bill Payment Numbers before making transactions.</div>
    </>
  );
};

export default SignUp;
