import React, { useEffect } from 'react'
import Modal from '../../../../components/modal/Modal'
import Button from '../../../../components/button/button'
import { useNavigate } from 'react-router-dom'

const TPSuccessModal = () => {
  const navigate = useNavigate()

  useEffect(() => {
    const timer = setTimeout(() => {
      navigate('/dashboard')
    }, 3000)

    return () => clearTimeout(timer) // Clean up the timer on component unmount
  }, [navigate])

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[500px]"
        position="modal-center"
        // onClose={closeModal}
        showCloseButton={false}
        title="Account Created"
      >
        <div className='flex flex-col'>
          <div className="text-center">
            <div className='py-5 flex items-center justify-center'>
              <img alt='img' src="/images/vectors/success.gif" width={100} height={100} />
            </div>

            <p className='text-xs font-[300]'>Your account has been successfully created. You will be redirected to the dashboard in a few seconds or click the button below to Proceed to Dashboard!</p>

            <div className='mt-10'>
              <Button
                onClick={() => navigate("/dashboard")}
                className={"w-full p-3 btn flex items-center justify-center gap-2 font-md text-xs"}
              >
                <span>Proceed to Dashboard</span>
              </Button>
            </div>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default TPSuccessModal