import { <PERSON>, useNavigate } from 'react-router-dom';
import React, { useState } from 'react';
import { AiOutlineEyeInvisible, AiOutlineEye } from "react-icons/ai";
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import Button from '../../../components/button/button';
import { ButtonLoader } from '../../../utils/ButtonLoader';
import Input from '../../../components/input/input';
import { TextUnder } from '../../../utils/Utils';
import { updateAccount } from '../../../../apis/account';
import { useCallModal } from '../../../hooks/useCallModal';
import TPSuccessModal from './components/TPSuccessModal';

const Createtransactionpin = () => {

  const [togglePin, setTogglePin] = useState(true)
  const [togglePin2, setTogglePin2] = useState(true)

  const handleToggle = () => setTogglePin(!togglePin)

  const handleToggle2 = () => setTogglePin2(!togglePin2)

  const [change_transaction_pin, setchange_transaction_pin] = useState({
    new: ""
  })

  const [confirmtransactionpin, setconfirmtransactionpin] = useState("")

  const {openModal, isOpenModal} = useCallModal()

  const { mutate, isPending, data } = useMutation({
    mutationFn: updateAccount,
    onSuccess: (data) => {
      toast.success(data?.detail)
      console.log("data", data)
      openModal()
    },
    onError: (error) => {
      toast.error(error.detail)
    }
  })

  const handleCreateTransactionPin = (e) => {
    e.preventDefault()
    if (change_transaction_pin.new !== confirmtransactionpin) {
      toast.error("Transaction Pin does not match")
      return;
    }
    mutate({ change_transaction_pin })
  }

  const check = !change_transaction_pin.new || !confirmtransactionpin || isPending


  return (
    <>
        {/* <div className='text-general_gray_text text-[14px]'>Step 4 of 4</div> */}

        <TextUnder header={"Create Your Transaction Pin"} content={"Set your 4 digit transaction pin to make your account secure"} />


        <form action="" onSubmit={handleCreateTransactionPin} className='w-full md:w-[60%]'>

          <div className='flex flex-col my-5'>
            <label htmlFor="transactionpin" className='mb-1 font-[400] text-[14px] text-general_text'>Set Transaction PIN <span className='text-text_orange'>*</span></label>
            <div className='flex items-center border rounded-[8px] pr-2'>
              <Input maxLength={4} name='new' value={change_transaction_pin?.new} onChange={(e) => setchange_transaction_pin({ new: e.target.value })} type={togglePin ? "password" : "text"} id='transactionpin' placeholder='Enter 4 digit pin' className='p-3 border-none' />
              <div onClick={handleToggle} className=' cursor-pointer'>
                {togglePin ?
                  <AiOutlineEye size={20} />
                  :
                  <AiOutlineEyeInvisible size={20} />
                }
              </div>
            </div>
          </div>

          <div className='flex flex-col my-5'>
            <label htmlFor="confirmtransactionpin" className='mb-1 font-[400] text-[14px] text-general_text'>Confirm Transaction PIN <span className='text-text_orange'>*</span></label>
            <div className='flex items-center border rounded-[8px] pr-2'>
              <Input maxLength={4} value={confirmtransactionpin} onChange={(e) => setconfirmtransactionpin(e.target.value)} type={togglePin2 ? "password" : "number"} id='confirmtransactionpin' placeholder='Enter 4 digit pin' className='p-3 border-none' />
              <div onClick={handleToggle2} className='cursor-pointer'>
                {togglePin2 ?
                  <AiOutlineEye size={20} />
                  :
                  <AiOutlineEyeInvisible size={20} />
                }
              </div>
            </div>
          </div>

          <div className='py-6'>
          <Button disabled={check} type='submit' className={`p-3 px-12 text-sm ${check ? 'btn_opacity' : ''}`}>
              {isPending ?
                <ButtonLoader />
                :
                <span>Submit</span>
              }
            </Button>
          </div>

          <div className='flex items-center text-general_text justify-center'>
            <Link to="/" className='text-[14px]'>Back to Login</Link>
          </div>

        </form>
        <div className='text-[12px] mt-4'>Your transactions are secure, always confirm Account Numbers, Phone Numbers and Bill Payment Numbers before making transactions.</div>

      {isOpenModal && data && (<TPSuccessModal />)}

    </>

  );
};

export default Createtransactionpin;