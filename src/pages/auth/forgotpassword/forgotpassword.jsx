import { Link, useNavigate } from 'react-router-dom';
import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { resetPassword } from '../../../../apis/account';
import toast from 'react-hot-toast';
import Button from '../../../components/button/button';
import { ButtonLoader } from '../../../utils/ButtonLoader';
import Input from '../../../components/input/input';
import { TextUnder } from '../../../utils/Utils';

const Forgotpassword = () => {

  const navigate = useNavigate();

  const [resetWith, setResetWith] = useState(true)

  const [formData, setFormData] = useState({
    reset_type: "password",
    verify_with: "email",
    country: "ng",
    user: "",
  })

  const handleToggleResetWith = () => {
    setResetWith(!resetWith);
    setFormData((prevData) => ({
      ...prevData,
      verify_with: resetWith ? "phone" : "email",
      user: ""
    }));
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value })
  }

  const { mutate: resetMutate, isPending } = useMutation({
    mutationFn: resetPassword,

    onSuccess: (data) => {
      // console.log(data)

      const requestDetail = formData
      localStorage.setItem("requestDetail", JSON.stringify(requestDetail))

      toast.success(`${data?.detail}`, {
        duration: 5000,
      })

      navigate("/createpassword")

    },
    onError: (error) => {
      console.log(error)
      toast.error(`${error?.detail}`, {
        duration: 5000,
      })
    }
  })

  const handleReset = (e) => {
    e.preventDefault(),
      resetMutate(formData)
  }

  const check = !formData?.user || isPending


  return (
    <>
        <TextUnder header={"Forgot Password"} content={"Enter  your Email to reset Password"}/>

        <form onSubmit={handleReset} className='w-full md:w-[60%]'>
          {resetWith ?
            <div className='flex flex-col my-4'>
              <label htmlFor="email_address" className='mb-1 font-[400] text-[14px] text-general_text'>Enter Email Address</label>
              <Input onChange={handleChange} value={formData?.user} name='user' type="email" id='email_address' placeholder='Enter Your Email Address' className='p-3' />
            </div>
            :
            <div className='flex flex-col my-4'>
              <label htmlFor="phone_number" className='mb-1 font-[400] text-[14px] text-general_text'>Enter Phone Number</label>
              <Input onChange={handleChange} value={formData?.user} name='user' type="tel" id='phone_number' maxLength={11} placeholder='Enter Your Phone Number' className='p-3' />
            </div>
          }

          <div className='flex items-center justify-end'>
            <div className='font-[500] text-[13px] text-auth_bg cursor-pointer' onClick={handleToggleResetWith}>{resetWith ? "Reset password with Phone Number" : "Reset password with Email"}</div>
          </div>

          <div className='my-5'>
          <Button disabled={check} type='submit' className={`p-3 px-12 text-sm ${check ? 'btn_opacity' : ''}`}>
              {isPending ?
                <ButtonLoader />
                :
                <span>Continue</span>
              }
            </Button>
          </div>

          <div className='flex items-center text-general_text justify-center text-[14px] mt-4'>
            <span className='mr-2'>Remember Password ?</span>
            <Link to="/" className=' text-auth_bg font-[500]'>Login</Link>
          </div>

        </form>
        <div className='text-[12px] mt-4'>Your transactions are secure, always confirm Account Numbers, Phone Numbers and Bill Payment Numbers before making transactions.</div>      
    </>
  );
};

export default Forgotpassword;