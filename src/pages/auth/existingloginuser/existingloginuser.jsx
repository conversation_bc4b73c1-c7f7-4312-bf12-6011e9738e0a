import { Link } from 'react-router-dom';
import React, { useState } from 'react';
import { AiOutlineEyeInvisible, AiOutlineEye } from "react-icons/ai";
import { PiUserSwitch } from "react-icons/pi";
import { TextUnder } from '../../../utils/Utils';


const Existingloginuser = () => {

  const [togglePassword, setTogglePassword] = useState(false)

  const handleTogglePassword = () => {
    setTogglePassword(!togglePassword)
  }

  return (
    <>
        <TextUnder header={"Welcome Back"} content={"Segun Ajisafe Isreal"} />

        {/* <Link href="/" className='text-text_orange font-[400] text-[14px] flex items-center'><span className='mr-2'><PiUserSwitch size={18} /></span> Switch Account</Link> */}

      <form action="" className='w-full md:w-[60%]'>

        <div className='flex flex-col my-5'>
          <label htmlFor="password" className='mb-1 font-[500] text-[15px] text-general_text'>Enter Password</label>
          <div className='flex items-center justify-between border-1 border p-4 py-3 border-border_color rounded-[8px] focus:border-none focus:border-0'>
            <input type={togglePassword ? "text" : "password"} id='password' placeholder='Enter your Password' className='text-general_text w-full placeholder:text-[15px] placeholder:font-[300] border-none focus:outline-none' />
            <div onClick={handleTogglePassword} className=' cursor-pointer'>
              {togglePassword ?
                <AiOutlineEye size={20} />
                :
                <AiOutlineEyeInvisible size={20} />
              }
            </div>
          </div>
        </div>

        <div className='flex items-center justify-end mb-4'>
          <Link to="/" className='font-[400] text-[14px] text-general_gray_text cursor-pointer'>Forgot Password?</Link>
        </div>

        <div className='py-2'>
          <button className='bg-auth_bg w-full py-3 rounded-[8px] text-text_auth font-[500] text-[16px]'>Login</button>
        </div>

        <div className='py-2'>
          <button className=' bg-text_auth w-full py-3 rounded-[8px] text-auth_bg border-border_color border font-[500] text-[14px] flex items-center justify-center'> <span><img src="/images/google_icon.png" className='mr-2' width={20} height={20} alt='img' /></span> Login with Google</button>
        </div>

        <div className='flex items-center text-general_text justify-center text-[14px] mt-4'>
          <span className='mr-2'>Don’t have an account?</span>
          <Link to="/signup" className=' text-auth_bg font-[500]'>Sign Up</Link>
        </div>

      </form>
      <div className='text-[12px] mt-4'>Your transactions are secure, always confirm Account Numbers, Phone Numbers and Bill Payment Numbers before making transactions.</div>
    </>
  );
};

export default Existingloginuser;