import { Link, useNavigate } from 'react-router-dom';
import React, { useEffect, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getAccount, resendVerificationCode<PERSON>pi, verifyAccountApi } from '../../../../apis/account';
import toast from 'react-hot-toast';
import { IoCheckmarkDoneSharp } from "react-icons/io5";
import Button from '../../../components/button/button';
import { ButtonLoader } from '../../../utils/ButtonLoader';
import Input from '../../../components/input/input';
import { TextUnder } from '../../../utils/Utils';

const Verify = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { data: accountData } = useQuery({
    queryFn: getAccount,
    queryKey: ["getaccount"]
  });

  // Add this effect to handle auto-redirect
  useEffect(() => {
    const isPhoneVerified = accountData?.verification?.phone_verification_status === "verified";
    const isEmailVerified = accountData?.verification?.email_verification_status === "verified";
    
    if (isPhoneVerified && isEmailVerified) {
      // Check if transaction pin is set
      const path = accountData?.is_transaction_pin_set ? "/dashboard" : "/createtransactionpin";
      navigate(path);
    }
  }, [accountData, navigate]);

  console.log("acc", getAccount)

  // State management
  const [userData, setUserData] = useState({
    email: "",
    phone_number: "",
    email_verification_code: "",
    phone_verification_code: ""
  });
  
  const [loading, setLoading] = useState({
    email: false,
    phone: false,
    emailVerification: false,
    phoneVerification: false
  });
  
  const [timers, setTimers] = useState({
    email: 300,
    phone: 300
  });

  // Get user data from localStorage
  useEffect(() => {
    const localStorageCredential = typeof window !== 'undefined' && 
      JSON.parse(localStorage.getItem("Kompat_userData"));

    if (localStorageCredential) {
      setUserData(prev => ({
        ...prev,
        email: localStorageCredential?.userData?.email || "",
        phone_number: localStorageCredential?.userData?.phone_number || ""
      }));
    }
  }, []);

  // Timer effects
  useEffect(() => {
    if (timers.email > 0) {
      const emailTimerId = setInterval(() => {
        setTimers(prev => ({ ...prev, email: prev.email - 1 }));
      }, 1000);
      return () => clearInterval(emailTimerId);
    }
  }, [timers.email]);

  useEffect(() => {
    if (timers.phone > 0) {
      const phoneTimerId = setInterval(() => {
        setTimers(prev => ({ ...prev, phone: prev.phone - 1 }));
      }, 1000);
      return () => clearInterval(phoneTimerId);
    }
  }, [timers.phone]);

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Mutations
  const { mutate: resendMutate } = useMutation({
    mutationFn: resendVerificationCodeApi,
    onSuccess: (data) => {
      toast.success(`${data?.detail}`, { duration: 5000 });
      queryClient.invalidateQueries(["getaccount"]);
      setLoading(prev => ({ ...prev, email: false, phone: false }));
    },
    onError: (error) => {
      toast.error(`${error?.detail}`, { duration: 5000 });
      setLoading(prev => ({ ...prev, email: false, phone: false }));
    }
  });

  const { mutate: verifyMutate } = useMutation({
    mutationFn: verifyAccountApi,
    onSuccess: (data) => {
      toast.success('Verified', { duration: 5000 });
      setLoading(prev => ({ ...prev, emailVerification: false, phoneVerification: false }));
      queryClient.invalidateQueries(["getaccount"]);
      // Remove the navigation from here since it's handled by the useEffect
    },
    onError: (error) => {
      toast.error(`${error?.detail}`, { duration: 5000 });
      setLoading(prev => ({ ...prev, emailVerification: false, phoneVerification: false }));
    }
  });

  // Handlers
  const handleResendEmailOtp = (e) => {
    e.preventDefault();
    setTimers(prev => ({ ...prev, email: 300 }));
    setLoading(prev => ({ ...prev, email: true }));
    resendMutate({ email: userData.email });
  };

  const handleResendPhoneNoOtp = (e) => {
    e.preventDefault();
    setTimers(prev => ({ ...prev, phone: 300 }));
    setLoading(prev => ({ ...prev, phone: true }));
    resendMutate({ phone_number: userData.phone_number });
  };

  const handleInputChange = (field, value) => {
    setUserData(prev => ({ ...prev, [field]: value }));
  };

  // Auto-verify when code length is 6
  useEffect(() => {
    if (userData.email_verification_code?.length === 6) {
      setLoading(prev => ({ ...prev, emailVerification: true }));
      verifyMutate({ email_verification_code: userData.email_verification_code });
    }
  }, [userData.email_verification_code, verifyMutate]);

  useEffect(() => {
    if (userData.phone_verification_code?.length === 6) {
      setLoading(prev => ({ ...prev, phoneVerification: true }));
      verifyMutate({ phone_verification_code: userData.phone_verification_code });
    }
  }, [userData.phone_verification_code, verifyMutate]);

  // Verification status
  const isPhoneVerified = accountData?.verification?.phone_verification_status === "verified";
  const isEmailVerified = accountData?.verification?.email_verification_status === "verified";
  const canProceed = isPhoneVerified && isEmailVerified;

  return (
    <>
      <TextUnder
        header={<>{!isEmailVerified ? 'Verify Email' : !isPhoneVerified ? 'Verify Phone Number' : (!isPhoneVerified && !isEmailVerified) ? "Verify Email and Phone Number" : ""}</>}
        content={"To proceed kindly complete the following verification"}
      />

      <form action="" className='w-full md:w-[60%]'>
        {!isPhoneVerified && userData.phone_number && (
          <div className='flex flex-col my-5'>
            <label htmlFor="phone" className='mb-1 font-[400] text-[14px] text-[#454946]'>
              Please enter the 6-digit code sent to {userData.phone_number?.slice(0, 4) + "****" + userData.phone_number?.slice(-4)}
            </label>
            <div className='flex border-1 border border-border_color rounded-[8px] overflow-hidden'>
              <Input
                disabled={isPhoneVerified}
                type="number"
                value={userData.phone_verification_code}
                onChange={(e) => handleInputChange("phone_verification_code", e.target.value)}
                pattern="\d{0,6}"
                onInput={(e) => { e.target.value = e.target.value.slice(0, 6); }}
                id='phone'
                placeholder='Enter your Phone verification code'
                className='p-3 border-none'
              />
              <button
                onClick={handleResendPhoneNoOtp}
                disabled={timers.phone > 0 || isPhoneVerified || loading.phone || loading.phoneVerification}
                className={`bg-auth_bg m-0 px-6 text-text_auth flex items-center ${timers.phone > 0 || isPhoneVerified || loading.phone || loading.phoneVerification ? 'btn_opacity' : 'cursor-pointer'}`}
              >
                <div className='text-[12px] font-[400] flex items-center justify-center'>
                  {isPhoneVerified ? (
                    <IoCheckmarkDoneSharp size={30} color='white' />
                  ) : loading.phone || loading.phoneVerification ? (
                    <ButtonLoader />
                  ) : (
                    timers.phone <= 0 ? <span>Resend Code</span> : formatTime(timers.phone)
                  )}
                </div>
              </button>
            </div>
          </div>
        )}

        {!isEmailVerified && userData.email && (
          <div className='flex flex-col my-5'>
            <label htmlFor="email" className='mb-1 font-[400] text-[14px] text-[#454946]'>
              Please enter the 6-digit code sent to {userData.email?.slice(0, 3)}****@gmail.com
            </label>
            <div className='flex border-1 border border-border_color rounded-[8px] overflow-hidden'>
              <Input
                disabled={isEmailVerified}
                type="number"
                value={userData.email_verification_code}
                onChange={(e) => handleInputChange("email_verification_code", e.target.value)}
                pattern="\d{0,6}"
                onInput={(e) => { e.target.value = e.target.value.slice(0, 6); }}
                id='email'
                placeholder='Enter your Email verification code'
                className='p-3 border-none'
              />
              <button
                onClick={handleResendEmailOtp}
                disabled={timers.email > 0 || isEmailVerified || loading.email || loading.emailVerification}
                className={`bg-auth_bg m-0 px-6 text-text_auth flex items-center ${timers.email > 0 || isEmailVerified || loading.email || loading.emailVerification ? 'btn_opacity' : 'cursor-pointer'}`}
              >
                <div className='text-[12px] font-[400] flex items-center justify-center'>
                  {isEmailVerified ? (
                    <IoCheckmarkDoneSharp size={30} color='white' />
                  ) : loading.email || loading.emailVerification ? (
                    <ButtonLoader />
                  ) : (
                    timers.email <= 0 ? <span>Resend Code</span> : formatTime(timers.email)
                  )}
                </div>
              </button>
            </div>
          </div>
        )}

        <div className='flex items-center flex-col justify-center'>
          <div className='text-general_gray_text text-[14px] mb-2'>Code Expires in 5 minutes</div>
        </div>

        <div className='py-6'>
          <Link to={getAccount?.is_transaction_pin_set ? "/dashboard" : "/createtransactionpin"}>
            <Button disabled={!canProceed} type='submit' className={`p-3 px-12 text-sm ${!canProceed ? 'btn_opacity' : ''}`}>
              <span>Proceed</span>
            </Button>
          </Link>
        </div>

        <div className='flex items-center text-general_text justify-center text-[14px]'>
          <Link to="/" className='hover:underline'>Back to Login</Link>
        </div>
      </form>
    </>
  );
};

export default Verify;
