import { Link, useNavigate } from 'react-router-dom';
import React, { useEffect, useState } from 'react';
import { AiOutlineEyeInvisible, AiOutlineEye } from "react-icons/ai";
import { useMutation } from '@tanstack/react-query';
import { completeReset, resetPassword } from '../../../../apis/account';
import toast from 'react-hot-toast';
import Button from '../../../components/button/button';
import { ButtonLoader } from '../../../utils/ButtonLoader';
import Input from '../../../components/input/input';
import { TextUnder } from '../../../utils/Utils';
import { GoCircle } from 'react-icons/go';
import { HiMiniCheckCircle } from 'react-icons/hi2';

const Createpassword = () => {
  const navigate = useNavigate()
  const [togglePassword, setTogglePassword] = useState(false);
  const [toggleConfirmPassword, setToggleConfirmPassword] = useState(false);

  const [time, setTime] = useState(0);
  const [formData, setFormData] = useState({
    reset_type: "password",
    verify_with: "",
    user: "",
    new: "",
    token: "",
    country: ""
  });

  const [otpIsLoading, setOtpIsLoading] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const localStorageCredentialToResetPassword = JSON.parse(localStorage.getItem("requestDetail"));
      if (localStorageCredentialToResetPassword) {
        setFormData((prevFormData) => ({
          ...prevFormData,
          verify_with: localStorageCredentialToResetPassword?.verify_with || "",
          user: localStorageCredentialToResetPassword?.user || "",
          country: localStorageCredentialToResetPassword?.country || ""
        }));
      }
    }
  }, [setFormData]);

  useEffect(() => {
    if (time > 0) {
      const timerId = setInterval(() => {
        setTime(prevTime => prevTime - 1);
      }, 1000);
      return () => clearInterval(timerId); // Clear interval on component unmount
    }
  }, [time]);

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleTogglePassword = () => setTogglePassword(!togglePassword);
  const handleToggleConfirmPassword = () => setToggleConfirmPassword(!toggleConfirmPassword);

  const { mutateAsync } = useMutation({ mutationFn: resetPassword })

  const handleResendOtp = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    setOtpIsLoading(true);
    try {
      const response = await mutateAsync(formData)
      toast.success(`${response?.detail}`)
    } catch (error) {
      toast.error(`${error?.detail}`)
    } finally {
      setOtpIsLoading(false);
    }
  }

  const [userPayload, setUserPayload] = useState({
    token: "",
    new: "",
  })

  console.log("token", userPayload?.token)

  const [confirmPassword, setConfirmPassword] = useState("")


  const { mutate, isPending } = useMutation({
    mutationFn: completeReset,
    onSuccess: (data) => {
      toast.success(`${data.detail}`)
      navigate("/dashboard")
    },
    onError: (error) => {
      toast.error(`${error?.detail}`)
    }
  })

  const handleSubmit = (e) => {
    e.preventDefault();
    if (userPayload?.new !== confirmPassword) {
      toast.error("Password does not match")
      return;
    }

    const payload = {
      ...formData,
      token: userPayload?.token,
      new: userPayload?.new,
    }
    mutate(payload)
  }

  const passwordRequirements = {
    length: userPayload.new.length > 8,
    lowerUpperCase: /[a-z]/.test(userPayload.new) && /[A-Z]/.test(userPayload.new),
    specialChar: /[!@#$%^&*(),.?":{}|<>]/.test(userPayload.new)
  };

  const check = !userPayload?.new || !confirmPassword || !userPayload?.token || isPending

  return (
    <>
        
        <TextUnder header={"Create New Password"} content={"Enter your Phone Number to reset Password"}/>

        <form action="" className='w-full md:w-[60%]' onSubmit={handleSubmit}>

          <div className='flex flex-col my-5'>
            <label htmlFor="otp" className='mb-1 font-[400] text-[14px] text-general_text'>Please enter the 6-digit code sent to {formData?.user?.slice(0, 3) + "****" + formData?.user?.slice(-4)}</label>
            <div className='flex border-1 border border-border_color rounded-[8px] overflow-hidden focus:border-none focus:border-0'>
              <Input
                pattern="\d{0,6}"
                onInput={(e) => { e.target.value = e.target.value.slice(0, 6); }}
                value={userPayload?.token} onChange={(e) => setUserPayload(prev => ({ ...prev, token: e.target.value }))} type="number" id='otp' placeholder='Enter your verification code' className='p-3 border-none' />
              <button
                onClick={handleResendOtp}
                disabled={time > 0 || otpIsLoading}
                className={`bg-auth_bg m-0 px-6 text-text_auth flex items-center ${time > 0 || otpIsLoading ? 'btn_opacity' : 'cursor-pointer'}`}
              > 
                <div className='text-[12px] font-[400] flex items-center justify-center'>
                  {otpIsLoading ? (
                    <ButtonLoader />
                  ) : (
                    time <= 0 ? <span>Resend Code</span> : formatTime(time)
                  )}
                </div>
              </button>
            </div>
          </div>

          <div className='flex flex-col my-5'>
            <label htmlFor="password" className='mb-1 font-[400] text-[14px] text-general_text'>Create your password</label>
            <div className='flex items-center border rounded-[8px] pr-2'>
              <Input value={userPayload?.new} onChange={(e) => setUserPayload(prev => ({ ...prev, new: e.target.value }))} type={togglePassword ? "text" : "password"} id='password' placeholder='Enter your Password' className='p-3 border-none' />
              <div onClick={handleTogglePassword} className=' cursor-pointer'>
                {togglePassword ?
                  <AiOutlineEye size={20} />
                  :
                  <AiOutlineEyeInvisible size={20} />
                }
              </div>
            </div>
          </div>

          <div>
          <div className='flex items-center my-2'>
            <div>
              {passwordRequirements.length ? (
                <HiMiniCheckCircle className="text-auth_bg" />
              ) : (
                <GoCircle />
              )}
            </div>
            <label htmlFor="check1" className={`ms-2 font-[400] text-[13px] ${passwordRequirements.length && 'text-green-600'}`}>Password must be more than 8 characters with at least 1 number</label>
          </div>
          <div className='flex items-center my-2'>
            <div>
              {passwordRequirements.lowerUpperCase ? (
                <HiMiniCheckCircle className="text-auth_bg" />
              ) : (
                <GoCircle />
              )}
            </div>
            <label htmlFor="check2" className={`ms-2 font-[400] text-[13px] ${passwordRequirements.lowerUpperCase && 'text-green-600'}`}>Password must contain both lower and upper case</label>
          </div>
          <div className='flex items-center my-2'>
            <div>
              {passwordRequirements.specialChar ? (
                <HiMiniCheckCircle className="text-auth_bg" />
              ) : (
                <GoCircle/>
              )}
            </div>
            <label htmlFor="check3" className={`ms-2 font-[400] text-[13px] ${passwordRequirements.specialChar && 'text-green-600'}`}>Password must contain a special character; e.g ()!”£$%&*</label>
          </div>
        </div>

          <div className='flex flex-col my-5'>
            <label htmlFor="password" className='mb-1 font-[400] text-[14px] text-general_text'>Confirm password</label>
            <div className='flex items-center border rounded-[8px] pr-2'>
              <Input value={confirmPassword} onChange={(e) => setConfirmPassword(e.target.value)} type={toggleConfirmPassword ? "text" : "password"} id='password' placeholder='Confirm your password' className='p-3 border-none' />
              <div onClick={handleToggleConfirmPassword} className=' cursor-pointer'>
                {toggleConfirmPassword ?
                  <AiOutlineEye size={20} />
                  :
                  <AiOutlineEyeInvisible size={20} />
                }
              </div>
            </div>
          </div>


          <div className='py-6'>
            <Button disabled={check} type='submit' className={`p-3 px-12 text-sm ${check ? 'btn_opacity' : ''}`}>
              {isPending ?
                <ButtonLoader />
                :
                <span>Continue</span>
              }
            </Button>
          </div>

          <div className='flex items-center text-general_text justify-center text-[14px] mt-4'>
            <span className='mr-2'>Remember Password ?</span>
            <Link to="/" className=' text-auth_bg font-[500]'>Login</Link>
          </div>

        </form>
        <div className='text-[12px] mt-4'>Your transactions are secure, always confirm Account Numbers, Phone Numbers and Bill Payment Numbers before making transactions.</div>

    </>
  );
};

export default Createpassword;
