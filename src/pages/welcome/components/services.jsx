import { GoArrowRight } from "react-icons/go";
import { Receivemoneycurrencytype } from "../../dashboard/receivemoney/components/receivemoneycurrencytype";
import { useServiceNavigation } from "../../../hooks/useServiceNavigation";
import useServiceStore from "../../../store/useServiceStore";
import ServicesVerification from "./ServicesVerification";

export const Services = ({ services }) => {
  const { 
    handleServiceClick, 
    isServiceSuspendedOrComingSoon, 
    isOpenModal, 
    closeModal,
    showVerificationModal,
    serviceVerifications
  } = useServiceNavigation();
  
  const { setSelectedService } = useServiceStore();
  
  const isSuspendedOrComingSoon = isServiceSuspendedOrComingSoon(services);

  const handleServiceSelection = () => {
    setSelectedService(services);
    handleServiceClick(services);
  };

  return (
    <>
      <div
        onClick={handleServiceSelection}
        className={`relative transition-all duration-500 bg-white group hover:bg-[#8680CF] border flex flex-col gap-6 p-8 rounded-[10px]
          ${isSuspendedOrComingSoon ? "opacity-50 cursor-not-allowed bg-none" : "cursor-pointer"}
        `}
      >
        <div>
          <img src={!services.icon ? '/images/logo/faviconblue.png' : services.icon} className="w-[30px] h-[30px]" />
        </div>
        <div className="font-bold text-lg text-auth_bg group-hover:text-white capitalize">
          {services?.name}
        </div>
        <div
          className="text-xs text-general_text group-hover:text-white truncate"
          dangerouslySetInnerHTML={{ __html: services?.description }}
        ></div>
        <div className="text-xs font-semibold text-auth_bg flex items-center gap-2 group-hover:text-white">
          <span className="whitespace-nowrap">Get Started</span>
          <GoArrowRight size={20} />
        </div>
        {services?.status === "coming soon" && (
          <div className="absolute top-0 right-0 m-3">
            <div className="capitalize rounded-lg flex items-center justify-center px-4 p-1 bg-[#DFE3FF] text-auth_bg text-[10px]">
              {services?.status}
            </div>
          </div>
        )}

        {services?.status === "suspended" && (
          <div className="absolute top-0 right-0 m-3">
            <div className="capitalize rounded-lg flex items-center justify-center px-4 p-1 bg-gray-400 text-white text-[10px]">
              {services?.status}
            </div>
          </div>
        )}
      </div>

      {/* Render the modal for "receive money" */}
      {isOpenModal && services?.name === "receive money" && (
        <Receivemoneycurrencytype closeModal={closeModal} />
      )}

      {/* Render the verification modal when needed */}
      {serviceVerifications && (
        <ServicesVerification
          verifications={serviceVerifications}
        />
      )}
    </>
  );
};
