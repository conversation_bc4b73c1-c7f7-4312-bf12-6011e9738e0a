import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import { getAllServicesVerification } from '../../../../apis/settings';
import Input from '../../../components/input/input';
import { IoCheckmarkDoneCircle } from 'react-icons/io5';
import { ButtonLoader, PageLoader } from '../../../utils/ButtonLoader';
import { DashboardHeaderContent } from '../../../utils/Utils';
import Navigateback from '../../../components/navigateback/navigateback';
import toast from 'react-hot-toast';
import Webcam from 'react-webcam';
import Button from '../../../components/button/button';
import { verifyDocument } from '../../../../apis/account';
import VerificationCom from '../../dashboard/myaccount/components/VerificationCom/VerificationCom';


const ServicesVerification = () => {
  const { slug } = useParams();
  const navigate = useNavigate();

  // Get country_code from localStorage
  const userData = JSON.parse(localStorage.getItem("Kompat_userData"));
  const country_code = userData?.userData?.location?.country?.alpha2code || "";

  const [payload, setPayload] = useState({
    document: 0,
    file: null,
    content: ""
  });

  // Add a separate state to track which document is currently being verified
  const [verifyingDocId, setVerifyingDocId] = useState(null);

  // Add webcam related states
  const [showCamera, setShowCamera] = useState(false);
  const [cameraError, setCameraError] = useState(false);
  const webcamRef = useRef(null);

  const { data: verificationData, isPending, refetch } = useQuery({
    queryKey: ['get-services-verification', slug],
    queryFn: () => getAllServicesVerification(slug, { country_code }),
  });

  // Reset camera error when showing camera
  useEffect(() => {
    if (showCamera) {
      setCameraError(false);
    }
  }, [showCamera]);

  // Mutation for document verification
  const { mutate: verifyDocumentMutation, isPending: isVerifyingDoc } = useMutation({
    mutationFn: verifyDocument,
    onSuccess: (data) => {
      toast.success(data?.detail || "Document verification submitted successfully");
      refetch(); // Refresh account data
      setPayload({ document: 0, file: null, content: "" }); // Reset form
      setVerifyingDocId(null); // Reset verifying document ID
    },
    onError: (error) => {
      toast.error(error?.detail || "Failed to verify document");
      setVerifyingDocId(null); // Reset verifying document ID on error
    }
  });

  const handleInputChange = (documentId, value) => {
    setPayload({
      document: documentId,
      content: value,
      file: null // Reset file when text input changes
    });
  };

  const handleFileChange = (documentId, file) => {
    setPayload({
      document: documentId,
      content: "", // Reset content when file changes
      file: file
    });
    console.log("File selected:", file.name, "for document ID:", documentId);
  };

  // Add webcam capture function
  const handleCapture = (documentId) => {
    if (!webcamRef.current) {
      toast.error("Camera not available");
      return;
    }

    const imageSrc = webcamRef.current.getScreenshot();
    if (!imageSrc) {
      toast.error("Failed to capture image");
      return;
    }

    // Convert base64 to file
    const base64Data = imageSrc.split(',')[1];
    const blob = base64ToBlob(base64Data, 'image/jpeg');
    const file = new File([blob], 'webcam-capture.jpg', { type: 'image/jpeg' });

    handleFileChange(documentId, file);
    setShowCamera(false);
    toast.success("Photo captured successfully");
  };

  // Add base64ToBlob helper function
  const base64ToBlob = (base64, type) => {
    const binaryString = window.atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return new Blob([bytes], { type: type });
  };

  const handleVerify = (documentId) => {
    // Get the document details
    const documentItem = verificationData?.results.find(
      item => item.verification_document.id === documentId
    );

    if (!documentItem) {
      toast.error("Document not found");
      return;
    }

    // Check if it's a file type document
    const isFileType = documentItem.verification_document.type === "file" ||
      documentItem.verification_document.type === "image";

    if (payload.document !== documentId || (isFileType ? !payload.file : !payload.content)) {
      toast.error("Please enter valid information");
      return;
    }

    // Set the verifying document ID before making the API call
    setVerifyingDocId(documentId);

    // Create form data for file uploads
    if (isFileType && payload.file) {
      const formData = new FormData();
      formData.append("document", documentId);
      formData.append("file", payload.file);
      // formData.append("service_id", verificationData?.service?.id);

      console.log("Submitting file:", payload.file.name, "for document ID:", documentId);
      verifyDocumentMutation(formData);
    } else {
      // For text-based documents
      verifyDocumentMutation({
        document: documentId,
        content: payload.content,
        // service_id: verificationData?.service?.id
      });
    }
  };

  // Add debugging logs to help identify the issue
  useEffect(() => {
    if (isVerifyingDoc) {
      console.log("Verification in progress, verifyingDocId:", verifyingDocId);
    }
  }, [isVerifyingDoc, verifyingDocId]);

  // Webcam configuration with flexible settings
  const videoConstraints = {
    facingMode: "user",
    width: { ideal: 1280 },
    height: { ideal: 720 }
  };

  if (isPending) {
    return <div className='w-full min-h-full flex items-center justify-center'>
      <PageLoader />
    </div>;
  }

  const verifications = verificationData?.results || [];

  return (
    <div className=".max-w-3xl mx-auto p-4">
      <Navigateback />

      <DashboardHeaderContent
        header={
          <>
            Service Verification - <span className='uppercase text-black font-semibold'>{verificationData?.service?.name}</span>
          </>
        }
        headerClassName={"text-lg"}
      />

      <VerificationCom
        accountQuery={verifications}
        isVerifyingDoc={isVerifyingDoc}
        handleVerify={handleVerify}
        showCamera={showCamera}
        payload={payload}
        handleInputChange={handleInputChange}
        setShowCamera={setShowCamera}
        webcamRef={webcamRef}
        handleCapture={handleCapture}
      />
    </div>
  );
};

export default ServicesVerification;