import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { getAllServices } from "../../../apis/settings";
import useServiceStore from "../../store/useServiceStore";
import Input from "../../components/input/input";
import { DashboardHeaderContent } from "../../utils/Utils";
import { BiSearch } from "react-icons/bi";
import { Services } from "./components/services";
import { PageLoader } from "../../utils/ButtonLoader";
import Navigateback from "../../components/navigateback/navigateback";

export const Welcomepage = () => {
  const [state, setState] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  
  const { setServices, setLoading, setError } = useServiceStore();

  const { data: getAllServicesQuery, isPending } = useQuery({
    queryKey: ["getallservices"],
    queryFn: getAllServices,
    onSuccess: (data) => {
      console.log("Services loaded:", data); // Debug log
      setServices(data);
      setLoading(false);
    },
    onError: (error) => {
      console.log("Services loaded:", data); // Debug log
      setError(error);
      setLoading(false);
    },
  });

  console.log("get", getAllServicesQuery)


  useEffect(() => {
    const localStorageCredential = typeof window !== 'undefined' && JSON.parse(localStorage.getItem("Kompat_userData"));

    if (localStorageCredential) {
      setState(localStorageCredential?.userData || "");
    }

  }, [])


  // console.log("state", state?.phone_number)

  // Filter services based on the search query
  const filteredServices = getAllServicesQuery?.results?.filter((service) =>
    service?.name?.toLowerCase().includes(searchQuery.toLowerCase())
  ); 

  return (
    <>
    <div>
      <Navigateback />
      <div>
        <DashboardHeaderContent
          header={
            <>
              <span>Welcome </span>
              <img
                src="images/vectors/wave.gif"
                alt="Gif"
                className="inline-block w-6 h-6" // Adjust size as needed
              />
              <span>, {state?.first_name + " " + state?.last_name}.</span>
            </>
          }
          subheader={"What would you like to do today?"}
          className={"flex items-center justify-center w-full flex-col gap-2"}
          subheaderClassName={"text-gray-500"}
        />

        <div className="flex items-center justify-center bg-white">
          <div className="flex items-center justify-center border rounded-[8px] p-4 sm:max-w-[60%] w-full">
            <BiSearch className="text-gray-500" />
            <Input
              placeholder={"Search for services"}
              type="search"
              value={searchQuery} // Bind the input value to the searchQuery state
              onChange={(e) => setSearchQuery(e.target.value)} // Update state when input changes
              className="border-0 pl-2"
            />
          </div>
        </div>
      </div>

      {isPending ? (
        <div className="w-full min-h-96 flex items-center justify-center">
          <PageLoader />
        </div>
      ) : filteredServices?.length === 0 ? (
        <div className="mt-10 bg-white p-10 rounded-[10px] shadow-md flex flex-col items-center justify-center min-h-60">
          <img 
            src="/images/vectors/no-results.svg" 
            alt="No results" 
            className="w-24 h-24 mb-4"
            onError={(e) => {
              e.target.onerror = null;
              // Use a more reliable fallback image
              e.target.src = "https://img.icons8.com/fluency/96/search-more.png";
            }}
          />
          <p className="text-gray-500 text-lg">No services found for "{searchQuery}"</p>
          <p className="text-gray-400 text-sm mt-2">Try a different search term</p>
        </div>
      ) : (
        <div className="mt-10 bg-white p-10 rounded-[10px] shadow-md overflow-scroll">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10">
            {filteredServices?.map((service) => (
              <Services services={service} key={service?.slug} />
            ))}
          </div>
        </div>
      )}
    </div>

    </>

  );
};
