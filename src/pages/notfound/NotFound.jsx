import React from 'react';
import { Link } from 'react-router-dom';
import Button from '../../components/button/button';

const NotFound = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 px-4">
      <div className="text-center max-w-md">

        <h1 className="text-8xl font-black text-gray-800 mb-2 uppercase tracking-wider">404</h1>
        <h1 className="text-lg font-bold text-gray-800 mb-2 uppercase">page Not Found</h1>
        <p className="text-gray-500 mb-8 text-base">
          The page you are looking for might have been removed, had its name changed,
          or is temporarily unavailable.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={() => window.history.back()}
            className="text-xs p-3 btn bg-gray-200 text-gray-800 hover:bg-gray-300"
          >
            Go Back
          </Button>

          <Button variant='primary' className="text-xs p-3 btn w-full">
            <Link to="/dashboard">
              Return to Dashboard
            </Link>
          </Button>
        </div>
      </div>

      <div className="mt-12 text-center text-sm text-gray-500">
        <p>Need assistance? <a href="#" className="text-auth_bg hover:underline">Contact Support</a></p>
      </div>
    </div>
  );
};

export default NotFound;