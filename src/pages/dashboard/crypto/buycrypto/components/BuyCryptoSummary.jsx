import { useQuery } from '@tanstack/react-query';
import Modal from '../../../../../components/modal/Modal';
import { cardConfig } from '../../../../../../apis/card';
import Button from '../../../../../components/button/button';
import { ButtonLoader } from '../../../../../utils/ButtonLoader';
import { useCallModal } from '../../../../../hooks/useCallModal';

export const BuyCryptoSummary = ({ selectedNetwork, selectedCurrency, closeModal }) => {
  
  const { data: configDetailQuery, isPending } = useQuery({
    queryKey: ['config-card', selectedCurrency],
    queryFn: () => cardConfig({ currency_code: selectedCurrency }),
    enabled: !!selectedCurrency,
  });

  const { isOpenModal, openModal, closeModal: closeVirtualSuccessModal } = useCallModal()

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[600px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title={`Buy ${selectedNetwork?.network_name} Crypto Summary`}
      >
        {isPending ? (
          <ButtonLoader />
        ) : configDetailQuery?.count === 0 ? (
          <div className="text-xs text-lightblack flex items-center text-center leading-5">
            There are no available charges for this currency.
          </div>
        ) : (
          <div>
            <div className='sm:px-10 pb-4'>
              {Object.entries(response?.display_data).map(([key, value]) => {
                // Skip the "currency" field
                if (["currency", "active"].includes(key) || value === null) {
                  return null;
                }

                console.log(value)
                // Render non-object values
                return (
                  <div key={key} className="flex items-center justify-between py-2 text-xs">
                    <div className="capitalize font-normal">{key.replace(/_/g, ' ')}</div>
                    <div className="font-medium">{value}</div>
                  </div>
                );
              })}

              <Button
                // onClick={handleProceed}
                // disabled={addCardPending}
                className="p-3 btn flex items-center justify-center gap-2 font-md text-xs mt-4"
              >
                <span>Proceed</span>
              </Button>
            </div>
          </div>


        )}

      </Modal>

      {/* {isOpenModal && (
        <VirtualSuccessModal closeModal={closeVirtualSuccessModal} />
      )} */}
    </>
  );
};