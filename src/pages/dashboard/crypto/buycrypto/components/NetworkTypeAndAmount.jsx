import { useState } from "react";
import Modal from "../../../../../components/modal/Modal";
import { useQuery } from "@tanstack/react-query";
import { currencycodeApi } from "../../../../../../apis/currency";
import { useCallModal } from "../../../../../hooks/useCallModal";
import { Select } from "antd";
import Input from "../../../../../components/input/input";
import Button from "../../../../../components/button/button";
import { BuyCryptoSummary } from "./BuyCryptoSummary";
import { PageLoader } from "../../../../../utils/ButtonLoader";
import toast from "react-hot-toast";

export const NetworkTypeAndAmount = ({ closeModal, selectedCurrency }) => {
  const [selectedNetwork, setSelectedNetwork] = useState(null);
  const [amount, setAmount] = useState("");
  const { isOpenModal, openModal, closeModal: closeQRModal } = useCallModal();

  const { data: networklist, isPending: isLoadingNetwork } = useQuery({
    queryKey: ["getallcryptonetworks", selectedCurrency],
    queryFn: () => currencycodeApi(selectedCurrency),
  });

  const handleProceed = (e) => {
    e.preventDefault();
    // Open the BuyCryptoSummary modal
    openModal();
  };

  const check = !selectedNetwork || !amount;

  console.log("Selected Network", selectedNetwork);

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Network Type and Amount"
      >
        <form className="overflow-hidden" onSubmit={handleProceed}>
          <div className="space-y-4">
            <Select
              size="large"
              className="w-full placeholder:text-[12px]"
              showSearch
              placeholder="Select Network"
              optionFilterProp="label"
              onChange={(value) => {
                // When a network is selected, store the full network object
                const selected = networklist?.crypto_network_list.find(
                  (network) => network.id === value
                );
                setSelectedNetwork(selected);
              }}
              options={networklist?.crypto_network_list?.map((network) => ({
                label: <div className="capitalize">{network.network_name}</div>,
                value: network.id,
              }))}
            />

            <Input
              type="number"
              placeholder={`Enter Amount of ${selectedCurrency} to purchase`}
              className="p-3"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
            />
          </div>

          <div className="mt-8">
            <Button
              type="submit"
              disabled={check}
              className={"p-3 btn flex items-center justify-center gap-2 font-md text-xs"}
            >
              <span>Proceed</span>
            </Button>
          </div>
        </form>
      </Modal>

      {isOpenModal && selectedNetwork && (
        <BuyCryptoSummary
          selectedCurrency={selectedCurrency}
          selectedNetwork={selectedNetwork}  // Passing the selected network object
          amount={amount}
          closeModal={closeQRModal}
        />
      )}
    </>
  );
};