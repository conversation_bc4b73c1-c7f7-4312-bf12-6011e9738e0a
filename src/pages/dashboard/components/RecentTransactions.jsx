import React from 'react'
import { formatDate, getStatusColor } from '../../../utils/Utils';

const RecentTransactions = ({res}) => {
  let textColor = getStatusColor(res?.status);
  
  return (
    <div className='flex items-center justify-between mt-6'>
      <div className='flex items-center gap-2'>
        <div className='rounded-full bg-[#EBECF5] p-[8px] flex items-center justify-center overflow-hidden w-[40px] h-[40px]'>
          <img src={"/images/services/cart.svg"} width={30} height={30} alt='img' objectfit='contain' />
        </div>
        <div>
          <div className='text-[13px] font-[500] capitalize'>{res?.transaction_type}</div>
          <div className='text-[11px] text-general_gray_text'>{formatDate(res?.created_on)}</div>
        </div>
      </div>
      <div className='flex justify-center items-end flex-col'>
        <div className='text-[13px] font-[500]'>{res?.currency_code} {parseFloat(res?.amount)?.toLocaleString()}</div>
        <div className='text-[11px] capitalize' style={{color: textColor}}>{res?.status}</div>
      </div>
    </div>
  )
}

export default RecentTransactions
