import { useQuery } from '@tanstack/react-query'
import React from 'react'
import { mostUsedServices } from '../../../../apis/settings'
import { useServiceNavigation } from '../../../hooks/useServiceNavigation'
import { Receivemoneycurrencytype } from '../receivemoney/components/receivemoneycurrencytype'
import { TbHandTwoFingers } from 'react-icons/tb'

const QuickActions = () => {
  const { data: mostUsedServicesQuery } = useQuery({ queryKey: ["most-used-services"], queryFn: mostUsedServices })

  const { handleServiceClick, isServiceSuspendedOrComingSoon, isOpenModal, closeModal } = useServiceNavigation();


  // images/logo/faviconblue.png
  return (
    <>
      <div>
        <div className='flex flex-col mt-10 gap-6 md:p-5 py-4 rounded-xl bg-white shadow-custom'>
          <div className='text-[18px] font-[500]'>Quick Actions</div>

          <div>
            <div className="flex items-center gap-4 overflow-x-auto no-scrollbar">
              {mostUsedServicesQuery?.results?.map((item) => {
                const isSuspendedOrComingSoon = isServiceSuspendedOrComingSoon(item);

                return (
                  <div
                    onClick={() => handleServiceClick(item)}
                    key={item.id}
                    className={`${isSuspendedOrComingSoon && 'btn_opacity opacity-50'} cursor-pointer rounded-xl bg-transparent_blue/60 hover:bg-transparent_blue transition-all duration-500 flex flex-col items-center justify-center gap-5 min-w-[120px] h-24`}>
                    {/* <img src={item.icon ? item.icon : "/images/logo/faviconblue.png"} className='overflow-hidden object-contain w-8' /> */}

                    <div>
                        <img src={!item.icon ? '/images/logo/faviconblue.png' : item?.icon} className="w-[30px] h-[30px]" />
                    </div>

                    <div className='capitalize whitespace-nowrap text-xs font-normal text-auth_bg'>{item.name}</div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {isOpenModal && (
        <div>
          <Receivemoneycurrencytype closeModal={closeModal} />
        </div>
      )}
    </>

  )
}

export default QuickActions