"use client"

import Tabparams from '../../../components/Tabparams/Tabparams'
import AccountDetails from './components/AccountDetails'
import AccountType from './components/AccountType'
import SecuritySettings from './components/SecuritySettings'
import { DashboardHeaderContent } from '../../../utils/Utils'
import React, { useState } from 'react'
import { FaArrowLeft } from "react-icons/fa6"
import { useNavigate } from 'react-router-dom'
import Verification from './components/Verification'
import ResetTransactionPin from './components/ResetTransactionPin'
import { useQuery } from '@tanstack/react-query'
import { getAccount } from '../../../../apis/account'

const Myaccount = () => {
  const navigate = useNavigate()

  const handleNavigateback = (e) => {
    e.preventDefault()
    navigate(-1)
  }

  const { handleSwitch, switchInfo } = Tabparams({ defaultTab: "accountdetail" });

  const { data: accountData, isPending: isLoadingAccount, refetch } = useQuery({
    queryFn: getAccount,
    queryKey: ["getaccount"]
  })

  return (
    <div>
      <div className='flex items-center justify-start'>
        <div className='flex items-center gap-3 mb-5 text-auth_bg cursor-pointer' onClick={handleNavigateback}>
          <FaArrowLeft />
          <span className='text-[13px]'>Go Back</span>
        </div>
      </div>

      <DashboardHeaderContent header="My Account" subheader="Manage and Edit your account information" />
      <div className="shadow-sm border rounded-lg border-border_color p-6">

        <div className='flex items-center gap-x-6 md:gap-x-6 border-b-1 border-0 overflow-x-auto no-scrollbar'>
          <div className={`flex items-center justify-center hover:text-auth_bg py-2 font-[500] text-[10px] md:text-[14px] whitespace-nowrap cursor-pointer ${switchInfo === "accountdetail" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
            onClick={() => handleSwitch("accountdetail")}>
            <div>Account Details</div>
          </div>

          <div className={`flex items-center hover:text-auth_bg justify-center py-2 font-[500] text-[10px] md:text-[14px] whitespace-nowrap cursor-pointer ${switchInfo === "securitysettings" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
            onClick={() => handleSwitch("securitysettings")}>
            <div>Security Settings</div>
          </div>

          <div className={`flex items-center hover:text-auth_bg justify-center py-2 font-[500] text-[10px] md:text-[14px] whitespace-nowrap cursor-pointer ${switchInfo === "accounttype" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
            onClick={() => handleSwitch("accounttype")}>
            <div>Account Type</div>
          </div>

          <div className={`flex items-center hover:text-auth_bg justify-center py-2 font-[500] text-[10px] md:text-[14px] whitespace-nowrap cursor-pointer ${switchInfo === "verifications" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
            onClick={() => handleSwitch("verifications")}>
            <div>Verification</div>
          </div>

          <div className={`flex items-center hover:text-auth_bg justify-center py-2 font-[500] text-[10px] md:text-[14px] whitespace-nowrap cursor-pointer ${switchInfo === "reset-transaction-pin" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
            onClick={() => handleSwitch("reset-transaction-pin")}>
            <div>Reset Transaction Pin</div>
          </div>
        </div>

        {switchInfo === "accountdetail" ? (
          <AccountDetails accountData={accountData} />
        ) : switchInfo === "accounttype" ? (
          <AccountType accountData={accountData} isLoadingAccount={isLoadingAccount}/>
        ) : switchInfo === "verifications" ? (
          <Verification accountQuery={accountData} isLoadingAccount={isLoadingAccount} refetch={refetch}/>
        ) : switchInfo === "reset-transaction-pin" ? (
          <ResetTransactionPin accountData={accountData}/>
        ) : (
          <SecuritySettings />
        )
        }

      </div>
    </div>
  )
}

export default Myaccount
