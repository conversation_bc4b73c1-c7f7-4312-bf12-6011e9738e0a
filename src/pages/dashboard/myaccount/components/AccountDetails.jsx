import React, { useEffect, useState } from 'react'
import Button from '../../../../components/button/button';
import Input from '../../../../components/input/input';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getAccount, updateAccount, updateProfilePhoto } from '../../../../../apis/account';
import { DashboardHeaderContent } from '../../../../utils/Utils';
import toast from 'react-hot-toast';
import { DatePicker, Select } from 'antd';
import { listOfCountries, listOfStates } from '../../../../../apis/location';
import { ButtonLoader } from '../../../../utils/ButtonLoader';
import dayjs from 'dayjs';

const AccountDetails = ({accountData}) => {

  const queryClient = useQueryClient();

  // console.log("account", data)

  const { mutateAsync: uploadProfilePhoto, isPending: isUploading } = useMutation({
    mutationKey: ["uploadprofilephoto"],
    mutationFn: updateProfilePhoto,
    onSuccess: () => {
      // Invalidate and refetch account data to get the updated profile_picture
      queryClient.invalidateQueries(["getaccount"]);
    },

  })

  const [profilePicture, setProfilePicture] = useState("/images/default.png");

  useEffect(() => {
    if (accountData?.profile_picture) {
      setProfilePicture(accountData.profile_picture);
    }
  }, [accountData])

  const handleFileChange = async (e) => {
    const selectedFile = e.target.files[0];

    if (selectedFile) {
      // Show preview immediately
      setProfilePicture(URL.createObjectURL(selectedFile));

      try {
        const formData = new FormData();
        formData.append("profile_picture", selectedFile);

        // Verify formData content
        console.log("FormData Content:", formData.get("profile_picture"));

        // Upload the file
        const response = await uploadProfilePhoto(formData);

        console.log("Upload Response:", response);
        toast.success(response?.detail || "Profile photo updated successfully.");
        setProfilePicture(response?.data?.profile_picture || profilePicture); // Update with server response
      } catch (error) {
        toast.error("Failed to upload profile photo.");
      }
    }
  };


  const { data: countryData, isPending: loadingCountries } = useQuery({
    queryKey: ["getallcountries"],
    queryFn: listOfCountries,
  });

  const genderOptions = [
    { label: "male", value: "male" },
    { label: "female", value: "female" },
  ]
  const [states, setStates] = useState([]);

  const [updatePayload, setUpdatePayload] = useState({
    first_name: "",
    last_name: "",
    gender: "",
    marital_status: "",
    location: {
      street_name: "",
      city: "",
      house_number: "",
      zip_code: "",
      country: "",
      state: "",
    },
    date_of_birth: "",
  });

  // Add the missing handleChange function
  const handleChange = (field, value) => {
    // Handle nested fields (like location.city)
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setUpdatePayload(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      // Handle top-level fields
      setUpdatePayload(prev => ({
        ...prev,
        [field]: value
      }));
    }

    // If changing country, fetch states for that country
    if (field === 'location.country') {
      fetchStates(value);
    }
  };

  useEffect(() => {
    if (accountData) {
      setUpdatePayload({
        first_name: accountData.first_name || "",
        last_name: accountData.last_name || "",
        gender: accountData.gender || "",
        marital_status: accountData.marital_status || "",
        location: {
          street_name: accountData.location?.street_name || "",
          city: accountData.location?.city || "",
          house_number: accountData.location?.house_number || "",
          zip_code: accountData.location?.zip_code || "",
          country: accountData.location?.country?.id || "",
          state: accountData.location?.state?.id || "",
        },
        date_of_birth: accountData.date_of_birth || "",
      });

      // If country exists, fetch states for that country
      if (accountData.location?.country?.alpha2code) {
        fetchStates(accountData.location.country.alpha2code, accountData.location?.state?.id);
      }
    }
  }, [accountData]);

  const fetchStates = async (countryCode, stateId = null) => {
    try {
      const stateResponse = await listOfStates(countryCode);
      setStates(stateResponse || []);
      
      // Only reset state if no stateId is provided (meaning it's a user-initiated country change)
      if (stateId === null) {
        setUpdatePayload(prev => ({
          ...prev,
          location: {
            ...prev.location,
            state: ""
          }
        }));
      }
    } catch (error) {
      console.error("Failed to fetch states:", error);
    }
  };

  const { mutateAsync, isPending: isUpdating } = useMutation({
    mutationKey: ["updateaccount"],
    mutationFn: updateAccount,
  });

  const handleSaveChanges = async () => {
    try {
      // Create a payload with only the fields that have values
      const cleanPayload = {};
      
      // Add first-level fields if they have values
      ['first_name', 'last_name', 'gender', 'marital_status', 'date_of_birth'].forEach(field => {
        if (updatePayload[field]) {
          cleanPayload[field] = updatePayload[field];
        }
      });
      
      // Add location fields if they have values
      if (Object.values(updatePayload.location).some(value => value)) {
        cleanPayload.location = {};
        Object.entries(updatePayload.location).forEach(([key, value]) => {
          if (value) {
            cleanPayload.location[key] = value;
          }
        });
      }
      
      const response = await mutateAsync(cleanPayload);
      toast.success(response?.detail || "Account updated successfully");
      queryClient.invalidateQueries(["getaccount"]);
    } catch (error) {
      toast.error(error?.detail || "Failed to update account");
      console.error("Failed to update account:", error);
    }
  };

  return (
    <div>

      <DashboardHeaderContent
        header={"Personal Infomation"}
        subheader={"Update and Edit yor personal details here."}
        subheaderClassName="text-gray-500 font-[300]"
        headerClassName="font-medium text-lg"
      />

      <hr className='h-1 w-full text-border_color my-4' />

      <form className='text-xs'>
        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <div className="md:leading-6 flex flex-col items-start justify-start">
              <div className="font-[500] text-[15px]">Your photo</div>
              <div className="font-[300] text-[13px]">This will be displayed on your profile.</div>
            </div>
          </div>
          <div className="col-span-12 md:col-span-8">
            <div className="w-full md:w-[500px]">
              <div className="flex items-start justify-between">
                <div className="relative h-16 w-16 rounded-full overflow-hidden">
                  {isUploading ? (
                    <div className="w-full h-full flex items-center justify-center bg-gray-100">
                      <span className="loader"><ButtonLoader /></span> {/* Add a spinner for loading */}
                    </div>
                  ) : (
                    <img
                      src={isUploading ? URL.createObjectURL(selectedFile) : accountData?.profile_picture || "/images/default.png"}
                      alt="Profile"
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>

                <div className="flex gap-4 items-center">
                  <label
                    htmlFor="upload-photo"
                    className="text-[13px] font-[500] text-auth_bg cursor-pointer"
                  >
                    Update
                  </label>
                  <input
                    id="upload-photo"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>First Name</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <div className='w-full md:w-[500px]'>

              <div className='flex items-center border rounded-[8px] pr-2'>
                <Input type='text'
                  placeholder="Enter your first name"
                  value={updatePayload.first_name}
                  onChange={(e) => handleChange("first_name", e.target.value)}
                  className={"p-3 border-none"} />
              </div>

            </div>
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>Last Name</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <div className='w-full md:w-[500px]'>

              <div className='flex items-center border rounded-[8px] pr-2'>
                <Input type='text'
                  placeholder="Enter your last name"
                  value={updatePayload.last_name}
                  onChange={(e) => handleChange("last_name", e.target.value)}
                  className={"p-3 border-none"} />
              </div>

            </div>
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>Email</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <div className='w-full md:w-[500px]'>
              <Input type='email' disabled={true} value={accountData?.email} className={"p-3 cursor-not-allowed"} />
            </div>
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>Phone Number</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <div className='w-full md:w-[500px]'>
              <Input type='tel' disabled={true} value={accountData?.phone_number} className={"p-3 cursor-not-allowed"} />
            </div>
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>Street Name</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <div className='w-full md:w-[500px]'>
              <div className='flex items-center border rounded-[8px] pr-2'>
                <Input type='text'
                  placeholder="Enter your street name"
                  value={updatePayload.location.street_name}
                  onChange={(e) => handleChange("location.street_name", e.target.value)}
                  className={"p-3 border-none"} />
              </div>
            </div>
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>House Number</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <div className='w-full md:w-[500px]'>
              <div className='flex items-center border rounded-[8px] pr-2'>
                <Input type='text'
                  placeholder="Enter your house number"
                  value={updatePayload.location.house_number}
                  onChange={(e) => handleChange("location.house_number", e.target.value)}
                  className={"p-3 border-none"} />
              </div>
            </div>
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>City</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <div className='w-full md:w-[500px]'>
              <div className='flex items-center border rounded-[8px] pr-2'>
                <Input type='text'
                  placeholder="Enter your city"
                  value={updatePayload.location.city}
                  onChange={(e) => handleChange("location.city", e.target.value)}
                  className={"p-3 border-none"} />
              </div>
            </div>
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>Zip Code</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <div className='w-full md:w-[500px]'>
              <div className='flex items-center border rounded-[8px] pr-2'>
                <Input type='text'
                  placeholder="Enter your zip code"
                  value={updatePayload.location.zip_code}
                  onChange={(e) => handleChange("location.zip_code", e.target.value)}
                  className={"p-3 border-none"} />
              </div>
            </div>
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>Marital Status</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <Select
              size='large'
              showSearch
              optionFilterProp="label"
              placeholder="Select Marital Status"
              value={updatePayload.marital_status}
              onChange={(value) => handleChange("marital_status", value)}
              options={[
                { label: "Single", value: "single" },
                { label: "Married", value: "married" },
                { label: "Divorced", value: "divorced" },
                { label: "Widowed", value: "widowed" }
              ]}
              className="w-full md:w-[500px] placeholder:text-[12px]"
            />
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>Country</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <Select
              size='large'
              showSearch
              optionFilterProp="label"
              placeholder="Select Country"
              value={countryData?.results?.find(country => country.id === updatePayload.location.country)?.alpha2code || ''}
              onChange={(value) => handleChange("location.country", value)}
              loading={loadingCountries}
              options={countryData?.results?.map((country) => ({
                label: country.name,
                value: country.alpha2code
              })) || []}
              className="w-full md:w-[500px] placeholder:text-[12px]"
            />

          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>State</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <Select
              size='large'
              showSearch
              optionFilterProp="label"
              placeholder="Select State"
              value={states?.find(state => state.id === updatePayload.location.state)?.id || ''}
              onChange={(value) => handleChange("location.state", value)}
              options={states?.map((state) => ({
                label: state.name,
                value: state.id
              })) || []}
              className="w-full md:w-[500px] placeholder:text-[12px]"
            />
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>Gender</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <Select
              size='large'
              showSearch
              optionFilterProp="label"
              placeholder="Select Gender"
              value={updatePayload.gender}
              onChange={(value) => handleChange("gender", value)}
              options={genderOptions}
              className="w-full md:w-[500px] placeholder:text-[12px]"
            />

          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>Date of Birth</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <DatePicker
              size='large'
              placeholder="Select Date of Birth"
              value={updatePayload.date_of_birth ? dayjs(updatePayload.date_of_birth) : null}
              onChange={(date) => handleChange("date_of_birth", date ? date.format("YYYY-MM-DD") : "")}
              className="w-full md:w-[500px] placeholder:text-[12px]"
            />
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className='flex items-center justify-center'>
          <div>
            <Button
              onClick={handleSaveChanges}
              // loading={isUpdating}
              type="button"
              className={`p-3 px-16 text-xs ${isUpdating && 'cursor-not-allowed'}`}
              disabled={isUpdating}>
              {isUpdating ? (
                <span><ButtonLoader /></span>
              ) : (
                <span>Save Changes</span>
              )}

            </Button>
          </div>
        </div>

      </form>



    </div>
  )
}

export default AccountDetails;