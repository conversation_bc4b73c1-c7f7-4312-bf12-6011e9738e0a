import React, { useState, useEffect } from 'react'
import { PhoneInput } from 'react-international-phone'
import Input from '../../../../components/input/input'
import { DashboardHeaderContent } from '../../../../utils/Utils'
import { useMutation } from '@tanstack/react-query'
import toast from 'react-hot-toast'
import Button from '../../../../components/button/button'
import { completeReset, resetPassword } from '../../../../../apis/account'

const ResetTransactionPin = ({ accountData }) => {
  const [formData, setFormData] = useState({
    reset_type: "transaction pin",
    verify_with: "email",
    country: "ng",
    user: accountData?.email || "", // Prefill with email
  });

  const [resetWith, setResetWith] = useState(true);

  // Update formData when toggle changes to prefill with either email or phone
  const handleToggle = () => {
    setResetWith(!resetWith);
    setFormData((prevData) => ({
      ...prevData,
      verify_with: resetWith ? "phone" : "email",
      user: resetWith ? accountData?.phone_number : accountData?.email, // Switch between email and phone
    }));
  };

  // Update initial form data when accountData changes
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      user: resetWith ? accountData?.email : accountData?.phone_number
    }));
  }, [accountData, resetWith]);
  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value })
  }

  const { mutate: resetMutate, isPending } = useMutation({
    mutationFn: resetPassword,

    onSuccess: (data) => {
      toast.success(`${data?.detail}`, {
        duration: 5000,
      })
    },
    onError: (error) => {
      console.log(error)
      toast.error(`${error?.detail}`, {
        duration: 5000,
      })
    }
  })

  const handleReset = () => {
    resetMutate(formData);
  };      

  const [completeResetPayload, setCompleteResetPayload] = useState({
    reset_type: "transaction pin",
    verify_with: resetWith ? "phone" : "email",
    user: resetWith ? accountData?.phone_number : accountData?.email, // Switch between email and phone
    country: "ng",
    token: "",
    new: ""
  })

  const handleChangeCompleteReset = (e) => {
    setCompleteResetPayload({ ...completeResetPayload, [e.target.name]: e.target.value })
  }

  const { mutate: completeResetMutation, isPending: isCompleting } = useMutation({
    mutationFn: completeReset,

    onSuccess: (data) => {
      toast.success(`${data?.detail}`, {
        duration: 5000,
      })
    },
    onError: (error) => {
      console.log(error)
      toast.error(`${error?.detail}`, {
        duration: 10000,
      })
    }
  })

  const handleCompleteReset = () => {
    if (!validatePins(completeResetPayload.new, confirmPin)) {
      return;
    }
    completeResetMutation(completeResetPayload);
  };

  const [confirmPin, setConfirmPin] = useState('');
  const [pinError, setPinError] = useState('');

  const validatePins = (pin, confirmPin) => {
    if (pin !== confirmPin) {
      setPinError('PINs do not match');
      return false;
    }
    setPinError('');
    return true;
  };

  const check = !completeResetPayload?.new || 
               !completeResetPayload?.token || 
               !confirmPin || 
               isCompleting || 
               pinError;

  return (
    <>
      <div>
        <DashboardHeaderContent
          header={"Reset Transaction Pin"}
          subheader={"Secure your transactions by resetting your PIN with email or phone verification"}
          subheaderClassName="text-gray-500 font-[300]"
          headerClassName="font-medium text-lg"
        />

        <form className='space-y-4'>

          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-12 md:col-span-4">
              <span>User</span>
            </div>

            <div className="col-span-12 md:col-span-8">
              <div className='w-full md:w-[500px]'>
                <React.Fragment>
                  <div className="flex gap-4 items-start">
                    <div className="flex-1">
                      {resetWith ? (
                        <div className='flex flex-col my-2'>
                          <Input
                            value={formData?.user}
                            name='user'
                            onChange={handleChange}
                            type="email"
                            id='email_address'
                            placeholder='Enter Your Email Address'
                            className='p-3 cursor-not-allowed'
                            disabled
                          />
                        </div>
                      ) : (
                        <div className='flex flex-col my-2'>
                          <PhoneInput
                            defaultCountry="ng"
                            value={formData?.user}
                            name='user'
                            onChange={(phone) => setFormData(prev => ({ ...prev, user: phone }))}
                            id="phone_number"
                            inputStyle={{ width: "100%", padding: "20px 10px", cursor: "not-allowed" }}
                            placeholder='Enter phone number'
                            disabled
                          />
                        </div>
                      )}
                      <div className='flex items-center justify-end'>
                        <div
                          className='font-medium text-[13px] text-auth_bg cursor-pointer'
                          onClick={handleToggle}
                        >
                          {resetWith ? "Receive OTP with Phone" : "Receive OTP with Email"}
                        </div>
                      </div>
                    </div>
                    <div className="mt-2">
                      <Button
                        onClick={handleReset}
                        loading={isPending}
                        disabled={!formData.user || isPending}
                        className="px-6 py-4 text-xs"
                      >
                        Request reset
                      </Button>
                    </div>
                  </div>


                </React.Fragment>
              </div>
            </div>

          </div>

          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-12 md:col-span-4">
              <span>Enter OTP</span>
            </div>
            <div className="col-span-12 md:col-span-8">
              <div className='w-full md:w-[500px]'>

                <div>
                  <Input
                    // disabled={isPhoneVerified}
                    type="number"
                    name={"token"}
                    value={completeResetPayload.token}
                    onChange={handleChangeCompleteReset}
                    pattern="\d{0,6}"
                    onInput={(e) => { e.target.value = e.target.value.slice(0, 6); }}
                    id='otp'
                    placeholder='Enter OTP'
                    className='p-3'
                  />
                </div>

              </div>
            </div>
          </div>


          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-12 md:col-span-4">
              <span>New Transaction Pin</span>
            </div>
            <div className="col-span-12 md:col-span-8">
              <div className='w-full md:w-[500px]'>
                <div>
                  <Input
                    // disabled={isPhoneVerified}
                    type="number"
                    name="new"
                    value={completeResetPayload.new}
                    onChange={handleChangeCompleteReset}
                    pattern="\d{0,4}"
                    onInput={(e) => { e.target.value = e.target.value.slice(0, 4); }}
                    id='otp'
                    placeholder='Enter New Transaction Pin'
                    className='p-3'
                  />
                </div>
              </div>
            </div>


          </div>

          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-12 md:col-span-4">
              <span>Confirm New Transaction Pin</span>
            </div>
            <div className="col-span-12 md:col-span-8">
              <div className='w-full md:w-[500px]'>
                <div>
                  <Input
                    type="number"
                    value={confirmPin}
                    onChange={(e) => {
                      const value = e.target.value.slice(0, 4);
                      setConfirmPin(value);
                      validatePins(completeResetPayload.new, value);
                    }}
                    pattern="\d{0,4}"
                    onInput={(e) => { e.target.value = e.target.value.slice(0, 4); }}
                    placeholder='Confirm Transaction Pin'
                    className='p-3'
                  />
                  {pinError && (
                    <span className="text-red-500 text-xs mt-1">{pinError}</span>
                  )}
                </div>
              </div>
            </div>

          </div>

          <div className='flex items-center justify-center pt-10'>
            <div>
              <Button
                onClick={handleCompleteReset}
                disabled={check}
                className="px-6 py-3 text-xs"
                loading={isCompleting}
              >
                Reset Transaction Pin
              </Button>
            </div>

          </div>

        </form>
      </div>
    </>
  )
}

export default ResetTransactionPin
