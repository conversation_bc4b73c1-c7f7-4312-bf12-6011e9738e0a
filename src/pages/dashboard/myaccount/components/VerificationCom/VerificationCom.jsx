import React, { useState, useRef } from 'react'
import { IoCheckmarkDoneCircle } from "react-icons/io5";
import Webcam from 'react-webcam';
import { MdPending } from 'react-icons/md';
import Input from '../../../../../components/input/input';
import { ButtonLoader } from '../../../../../utils/ButtonLoader';

const VerificationCom = ({accountQuery, isVerifyingDoc, handleVerify, showCamera, payload, handleInputChange, setShowCamera, webcamRef, handleCapture}) => {
  console.log("accountQuery", accountQuery)
  return (
      <div className="space-y-6">
      {accountQuery?.map((item, index) => {
        const document = item?.document || item?.verification_document
        const isVerifying = isVerifyingDoc && payload?.document === document.id;
        const isVerified = item.verification_status === "verified";
        const isProcessing = item.verification_status === "processing";
        const isDisabled = isVerified || isProcessing || isVerifying;
        const isFileType = document?.type === "file" || document?.type === "image";
        const shouldUseCamera = item.camera_only && isFileType;

        console.log("item", item)

        return (
          <React.Fragment key={document.id}>
            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-12 md:col-span-4 capitalize">
                <span>{document?.name} <span className={`${item?.verification_status === "verified" ? 'text-green-600 bg-green-600/20 rounded-md px-2 p-1 text-[10px]' : 'border rounded-md px-2 p-1 text-[10px]'}`} >{item?.verification_status || item?.status}</span></span>
              </div>
              <div className="col-span-12 md:col-span-8">
                <div className='w-full md:w-[500px]'>
                  {isDisabled ? (
                    <div className='flex items-center border rounded-[8px] pr-2'>
                      <Input value={`${item?.submitted_data ? item?.submitted_data : ""}`} className={"p-3 border-none"} disabled />
                      {isVerified ? (
                        <IoCheckmarkDoneCircle size={30} className='text-green-600' />
                      ) : isProcessing ? (
                        <MdPending size={30} className='text-yellow-500' />
                      ) : isVerifying ? (
                        <ButtonLoader />
                      ) : null}
                    </div>
                  ) : (
                    <div className='flex flex-col border rounded-[8px] overflow-hidden'>
                      {showCamera && shouldUseCamera ? (
                        <div className="relative">
                          <Webcam
                            ref={webcamRef}
                            screenshotFormat="image/jpeg"
                            className="w-full"
                            mirrored={true}
                          />
                          <div className="flex justify-center gap-2 p-2 bg-white">
                            <button
                              className="bg-auth_bg px-4 py-2 text-white rounded text-sm"
                              onClick={() => handleCapture(item.document.id)}
                            >
                              Take Photo
                            </button>
                            <button
                              className="bg-red-500 px-4 py-2 text-white rounded text-sm"
                              onClick={() => setShowCamera(false)}
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className='flex items-center h-full'>
                          {isFileType && shouldUseCamera ? (
                            <div className="flex-1 flex items-center">
                              <button
                                className="w-full p-3 text-left text-gray-500 hover:bg-gray-50 transition-colors"
                                onClick={() => setShowCamera(true)}
                              >
                                {payload.file ? 'Photo captured - Click to retake' : `Click to take a selfie for ${document?.name}`}
                              </button>
                            </div>
                          ) : (
                            <Input
                              type={document.type}
                              placeholder={`Enter your ${document?.name} (${document.minimum_input}-${document.maximum_input} characters)`}
                              className={"p-3 border-none"}
                              disabled={isDisabled}
                              onChange={(e) => {
                                const maxLength = parseInt(document.maximum_input);
                                const value = maxLength ? e.target.value.slice(0, maxLength) : e.target.value;
                                handleInputChange(document.id, value);
                              }}
                              value={payload.document === document.id ? payload.content : ""}
                            />
                          )}
                          <button
                            disabled={isDisabled || (payload.document !== document.id || (isFileType ? !payload.file : !payload.content))}
                            className={`bg-auth_bg m-0 px-6 text-text_auth flex items-center justify-center h-full py-3 ${(isDisabled || (payload.document !== document.id || (isFileType ? !payload.file : !payload.content))) ? 'btn_opacity' : ''}`}
                            onClick={() => handleVerify(document.id)}
                          >
                            <span className='text-[12px]'>
                              {isVerifying ? "Verifying..." : "Verify"}
                            </span>
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {index < accountQuery.length - 1 && (
              <hr className='h-1 w-full text-border_color my-4' />
            )}
          </React.Fragment>
        )
      })}
      </div>
  )
}

export default VerificationCom
