import Button from '../../../../components/button/button'
import Input from '../../../../components/input/input'
import { AiOutlineEyeInvisible, AiOutlineEye } from "react-icons/ai";
import React, { useState } from 'react'
import toast from 'react-hot-toast'
import Modal from '../../../../components/modal/Modal'
import useModal from '../../../../hooks/useModal'
import { ButtonLoader } from '../../../../utils/ButtonLoader'
import { useMutation } from '@tanstack/react-query'
import { updateAccount } from '../../../../../apis/account';
import { useCallModal } from '../../../../hooks/useCallModal';
import ModalTP from './SecuritysettingModal/ModalTP';



const TransactionPin = () => {
  const { openModal, isOpenModal, closeModal } = useCallModal()

  // const handleOpenModal = () => {
  //     openModal(
  //         "Pin Updated Successfully",
  //         <div>
  //             <div className='text-center flex items-center justify-center py-5'>
  //                 <img src="/images/vectors/success.gif" alt='img' width={100} height={100} />
  //             </div>
  //             <p className='text-[14px] font-[400]'>Updated Successfully</p>
  //             <p className='text-sm font-[300]'>You successfully updated your transaction pin.</p>
  //         </div>,
  //         'Back to Dashboard',
  //         () => {
  //             navigate("/dashboard")
  //         }
  //     )
  // }

  // pin toggles
  const [togglePins, setTogglePins] = useState({
    pin1: false,
    pin2: false,
    pin3: false
  })

  const handleTogglePins = (key) => {
    setTogglePins(prev => ({ ...prev, [key]: !prev[key] }))
  }

  const [confirmnewpin, setconfirmnewpin] = useState("")

  // change pin states and logic
  const [change_transaction_pin, setchange_transaction_pin] = useState({
    old: "",
    new: ""
  })

  const { mutate, isPending } = useMutation({
    mutationFn: updateAccount,
    onSuccess: (data) => {
      console.log("data", data)
      toast.success(`${data?.detail}`)
      openModal()
    },
    onError: (error) => {
      console.log("error", error)
      toast.error(error?.detail)
    }
  })

  const handleChangeTransactionPin = (e) => {
    e.preventDefault()
    if (change_transaction_pin?.new !== confirmnewpin) {
      toast.error("Confirm New Pin does not match with New Pin field")
      return;
    }
    mutate({ change_transaction_pin })
  }

  const check = !change_transaction_pin?.new || !change_transaction_pin?.old || !confirmnewpin || isPending;



  return (
    <div className="shadow-sm border rounded-lg border-border_color py-6">

      <div className='md:leading-6 px-6'>
        <div className='font-[500] text-[16px]'>Transaction Pin</div>
        <div className='font-[300] text-[12px]'>Enter Current Password to update your password</div>
      </div>

      <hr className='h-1 w-full text-border_color my-4' />

      <form action="" onSubmit={handleChangeTransactionPin} className='px-6'>

        <div className='flex flex-col my-5'>
          <label htmlFor="currentpin" className='mb-1 font-[500] text-[14px] text-general_text'>Current Pin</label>
          <div className='flex items-center pr-2 border rounded-[8px]'>
            <Input maxLength={4} id={"currentpin"} placeholder={"Current Pin"} type={togglePins?.pin1 ? "number" : "password"} className={"p-3 border-none"} value={change_transaction_pin?.old} onChange={(e) => setchange_transaction_pin(prev => ({ ...prev, old: e.target.value.slice(0, 4) }))} />
            <div onClick={() => handleTogglePins("pin1")} className='cursor-pointer'>
              {togglePins?.pin1 ?
                <AiOutlineEye size={20} />
                :
                <AiOutlineEyeInvisible size={20} />
              }
            </div>
          </div>
        </div>

        <div className='flex flex-col my-5'>
          <label htmlFor="newpin" className='mb-1 font-[500] text-[14px] text-general_text'>Enter New Pin</label>
          <div className='flex items-center pr-2 border rounded-[8px]'>
            <Input maxLength={4} id={"newpin"} placeholder={"Enter New Pin"} type={togglePins?.pin2 ? "number" : "password"} className={"p-3 border-none"} value={change_transaction_pin?.new} onChange={(e) => setchange_transaction_pin(prev => ({ ...prev, new: e.target.value.slice(0, 4) }))} />
            <div onClick={() => handleTogglePins("pin2")} className='cursor-pointer'>
              {togglePins?.pin2 ?
                <AiOutlineEye size={20} />
                :
                <AiOutlineEyeInvisible size={20} />
              }
            </div>
          </div>
        </div>

        <div className='flex flex-col my-5'>
          <label htmlFor="confirmnewpin" className='mb-1 font-[500] text-[14px] text-general_text'>Confirm New Pin</label>
          <div className='flex items-center pr-2 border rounded-[8px]'>
            <Input maxLength={4} id={"confirmnewpin"} placeholder={"Confirm New Pin"} type={togglePins?.pin3 ? "number" : "password"} className={"p-3 border-none"} value={confirmnewpin} onChange={(e) => setconfirmnewpin(e.target.value.slice(0, 4))} />
            <div onClick={() => handleTogglePins("pin3")} className='cursor-pointer'>
              {togglePins?.pin3 ?
                <AiOutlineEye size={20} />
                :
                <AiOutlineEyeInvisible size={20} />
              }
            </div>
          </div>
        </div>

        <div className='flex items-center justify-end mt-10'>
          <div>
            <Button disabled={check} type='submit' className={`p-3 px-12 text-xs ${check ? 'btn_opacity' : ''}`}>
              {isPending ?
                <ButtonLoader />
                :
                <span>Save Changes</span>
              }
            </Button>
          </div>
        </div>

      </form>
      {isOpenModal && (<ModalTP />)}

    </div>
  )
}

export default TransactionPin
