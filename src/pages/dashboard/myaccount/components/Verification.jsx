import React, { useState, useRef } from 'react'
import { DashboardHeaderContent, getStatusColor } from '../../../../utils/Utils'
import Input from '../../../../components/input/input'
import { IoCheckmarkDoneCircle } from "react-icons/io5";
import { useQuery, useMutation } from '@tanstack/react-query';
import { getAccount, verifyDocument } from '../../../../../apis/account';
import toast from 'react-hot-toast';
import Webcam from 'react-webcam';
import { MdPending } from 'react-icons/md';
import { ButtonLoader, PageLoader } from '../../../../utils/ButtonLoader';
import VerificationCom from './VerificationCom/VerificationCom';


const Verification = ({accountQuery, isLoadingAccount, refetch}) => {

  const [payload, setPayload] = useState({
    document: 0,
    file: null,
    content: ""
  })

  const handleVerify = (documentId) => {
    // Get the document details
    const documentItem = accountQuery?.verification?.account_type_verification?.verifications.find(
      item => item.document.id === documentId
    );

    if (!documentItem) {
      toast.error("Document not found");
      return;
    }

    // Fix the isFileType check to properly evaluate the condition
    const isFileType = documentItem.document.type === "file" || documentItem.document.type === "image";

    if (payload.document !== documentId || (isFileType ? !payload.file : !payload.content)) {
      toast.error("Please enter valid information");
      return;
    }

    // Create form data for file uploads
    if (isFileType && payload.file) {
      const formData = new FormData();
      formData.append("document", documentId);
      formData.append("file", payload.file);

      console.log("Submitting file:", payload.file.name, "for document ID:", documentId);
      verifyDocumentMutation(formData);
    } else {
      verifyDocumentMutation(payload);
    }
  }

  const handleInputChange = (documentId, value) => {
    setPayload({
      document: documentId,
      content: value,
      file: null // Reset file when text input changes
    });
  }

  const handleFileChange = (documentId, file) => {
    setPayload({
      document: documentId,
      content: "", // Reset content when file changes
      file: file
    });
    console.log("File selected:", file.name, "for document ID:", documentId);
  }

  const { mutate: verifyDocumentMutation, isPending: isVerifyingDoc } = useMutation({
    mutationFn: verifyDocument,
    onSuccess: (data) => {
      toast.success(data?.detail || "Document verification submitted successfully");
      refetch(); // Refresh account data
      setPayload({ document: 0, file: null, content: "" }); // Reset form
    },
    onError: (error) => {
      toast.error(error?.detail || "Failed to verify document");
    }
  });

  const [showCamera, setShowCamera] = useState(false);
  const webcamRef = useRef(null);

  const handleCapture = (documentId) => {
    const imageSrc = webcamRef.current.getScreenshot();
    // Convert base64 to file
    const base64Data = imageSrc.split(',')[1];
    const blob = base64ToBlob(base64Data, 'image/jpeg');
    const file = new File([blob], 'webcam-capture.jpg', { type: 'image/jpeg' });

    handleFileChange(documentId, file);
    setShowCamera(false);
  };

  const base64ToBlob = (base64, type) => {
    const binaryString = window.atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return new Blob([bytes], { type: type });
  };

  return (

    <>
      {(isLoadingAccount) ? (
        <PageLoader />
    ) : (
    <div>
      <DashboardHeaderContent
        header={"Verification"}
        subheader={"Verify all required to enjoy a better experience from Kompat"}
        subheaderClassName="text-gray-500 font-[300]"
        headerClassName="font-medium text-lg"
      />

      <hr className='h-1 w-full text-border_color my-4' />

      <div className="grid grid-cols-12 gap-6">
        <div className="col-span-12 md:col-span-4 capitalize">
          <span>Phone Number <span className='text-green-600  bg-green-600/20 rounded-md px-2 p-1 text-[10px]'>{accountQuery?.verification?.phone_verification_status}</span></span>
        </div>
        <div className="col-span-12 md:col-span-8">
          <div className='w-full md:w-[500px]'>
            <div className='flex items-center border rounded-[8px] pr-2'>
              <Input value={`${accountQuery?.phone_number}`} className={"p-3 border-none"} disabled />
              <div><IoCheckmarkDoneCircle size={30} className='text-green-600' /></div>
            </div>
          </div>
        </div>
      </div>

      <hr className='h-1 w-full text-border_color my-4' />

      <div className="grid grid-cols-12 gap-6">
        <div className="col-span-12 md:col-span-4 capitalize">
          <span>Email <span className='text-green-600 bg-green-600/20 rounded-md px-2 p-1 text-[10px]'>{accountQuery?.verification?.email_verification_status}</span></span>
        </div>
        <div className="col-span-12 md:col-span-8">
          <div className='w-full md:w-[500px]'>
            <div className='flex items-center border rounded-[8px] pr-2'>
              <Input value={`${accountQuery?.email}`} className={"p-3 border-none"} disabled />
              <div><IoCheckmarkDoneCircle size={30} className='text-green-600' /></div>
            </div>
          </div>
        </div>
      </div>

      <hr className='h-1 w-full text-border_color my-4' />

      <VerificationCom
        accountQuery={accountQuery?.verification?.account_type_verification?.verifications}
        isVerifyingDoc={isVerifyingDoc}
        handleVerify={handleVerify}
        showCamera={showCamera}
        payload={payload}
        handleInputChange={handleInputChange}
        webcamRef={webcamRef}
        setShowCamera={setShowCamera}
        handleCapture={handleCapture}
      />
    </div>
        )}
    </>
  )
}

export default Verification;