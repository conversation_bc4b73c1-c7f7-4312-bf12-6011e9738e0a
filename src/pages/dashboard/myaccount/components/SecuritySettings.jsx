import React from 'react'
import TransactionPin from './TransactionPin';
import ChangePassword from './ChangePassword';
import { DashboardHeaderContent } from '../../../../utils/Utils';


const SecuritySettings = () => {

  return (
    <div>

      <DashboardHeaderContent
        header={"Security Information"}
        subheader={"Edit your password and transation PIN"}
        subheaderClassName="text-gray-500 font-[300]"
        headerClassName="font-medium text-lg" 
      />

      <hr className='h-1 w-full text-border_color my-4' />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

        <ChangePassword />

        <TransactionPin />
      </div>

    </div>
  )
}

export default SecuritySettings