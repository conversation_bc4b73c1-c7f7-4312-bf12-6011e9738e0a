import { DashboardHeaderContent } from '../../../../utils/Utils';
import { getAccountType } from '../../../../../apis/settings';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { changeAccountType, getAccount } from '../../../../../apis/account';
import { FiMinus, FiPlus } from 'react-icons/fi';
import { ButtonLoader, PageLoader } from '../../../../utils/ButtonLoader';
import { MdVerifiedUser } from "react-icons/md";
import { LiaTimesSolid } from "react-icons/lia";
import { IoChevronDownOutline, IoChevronUp } from "react-icons/io5";
import { useNavigate } from 'react-router-dom';
import { IoMdSwitch } from "react-icons/io";
import { toast } from 'react-hot-toast';

const AccountType = ({ accountData, isLoadingAccount }) => {
  const queryClient = useQueryClient();

  const [expandedItem, setExpandedItem] = useState(null);
  const [expandedService, setExpandedService] = useState(null);
  const [showVerification, setShowVerification] = useState(false);
  const navigate = useNavigate()

  const { data: accountTypeQuery, isPending: isLoadingAccountType } = useQuery({
    queryKey: ["getaccount-type"],
    queryFn: getAccountType
  });

  const handleToggleExpand = (itemName) => {
    setExpandedItem(expandedItem === itemName ? null : itemName);
  };

  const handleToggleService = (serviceName) => {
    setExpandedService(expandedService === serviceName ? null : serviceName);
  };

  const [formData, setData] = useState({
    account_type: 0,
    callback_url: ""
  })


  const { mutateAsync: changeAccountTypeMutation, isPending: changingAccountType, error: mutationError } = useMutation({
    mutationFn: (data) => changeAccountType(data),
    onSuccess: (data) => {
      toast.success(data?.detail || "Account type changed successfully");
      queryClient.invalidateQueries(["getaccount", "getaccount-type"]);

      if (data?.payment_link) {
        window.location.href = data?.payment_link;
      }
    },
    onError: (error) => {
      console.error('Mutation error:', error);
      toast.error(error?.detail || "Failed to change account type");
    }
  })

  let domain = window.location.host;
  let protocol = window.location.protocol;
  const callBackUrl = `${protocol}//${domain}/dashboard/myaccount`;

  const handleSwitchAccountType = (verifications, accountTypeId) => {
    // Check if all verifications are completed
    const allVerified = verifications?.every(
      (verification) => verification.verification_status === "verified"
    );

    if (allVerified) {
      // If all verified, call mutation to change account type
      changeAccountTypeMutation({
        account_type: accountTypeId,
        callback_url: callBackUrl
      });
    } else {
      // If any verification is pending, navigate to verification page
      navigate(`/dashboard/account-type-verification`, {
        state: { verifications }
      });
    }
  };

  const renderServiceLimits = (service) => {
    return (
      <>
        {service?.limits.length > 0 && (

          <div className="px-6 py-4 bg-gray-50 rounded-md space-y-6">
            <div className='text-sm'>Transaction Limit</div>
            {service.limits.map((limitItem, index) => (
              <div key={index} className="space-y-2 border p-4">
                <div className="text-gray-600 text-xs capitalize">
                  {limitItem.limit}
                </div>
                {/* {index === 0 && ( */}
                <div className="relative h-1 bg-gray-200 rounded-full overflow-hidden">
                  <div className="absolute left-0 top-0 h-full w-1/4 bg-primary rounded-full" />
                </div>
                {/* )} */}
                <span className='text-xs'>
                  {Number(limitItem.limit_value).toLocaleString()}
                </span>
              </div>
            ))}
          </div>
        )}

      </>

    );
  };

  const renderVerificationRequirements = (item) => {
    console.log("item", item)
    return (
      <div className="mt-4 p-4 border rounded-lg shadow-sm">
        <div className="space-y-6">
          <div className="flex items-start justify-between">

            <div className='flex flex-col justify-center items-start'>
              <h3 className="text-sm font-medium text-gray-900">Required Document Upgrade</h3>
              <p className="text-gray-500 text-xs">Documents needed for upgrading your account limit</p>
            </div>

            <span
              onClick={(e) => {
                e.stopPropagation();
                setShowVerification(false);
              }}
              className='rounded-full p-1 hover:bg-gray-200'><LiaTimesSolid className="h-3 w-3" /></span>
          </div>

          <div className="space-y-4">
            {item?.verifications?.map((doc, index) => (
              <div key={index} className="text-xs flex items-center gap-3">
                <div className="text-green-600 border border-green-600 p-[2px] rounded-full">
                  <MdVerifiedUser className="h-4 w-4" />
                </div>
                <span className="text-gray-900 font-medium capitalize">{doc?.document?.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {(isLoadingAccount || isLoadingAccountType) ? (
        <PageLoader />
      ) : (
        <div className="max-w-4xl .mx-auto">
          <DashboardHeaderContent
            header="Account Type"
            subheader={accountData?.current_account_type?.name}
            subheaderClassName="text-green-600 font-[300] capitalize"
            headerClassName="font-medium text-base"
          />

          <div className="space-y-4 mt-6">
            {accountTypeQuery?.results?.map((item) => (
              <div key={item.id} className="grid grid-cols-12 gap-6">
                <div className="col-span-12 md:col-span-4">
                  <div className="flex items-center gap-4">
                    {item.icon && <img src={item.icon} className="h-4 w-4" />}
                    <span className="font-medium capitalize">{item.name}</span>
                  </div>
                </div>

                <div className="col-span-12 md:col-span-8">
                  <div className="overflow-hidden">
                    <button
                      onClick={() => handleToggleExpand(item.name)}
                      className="rounded-md shadow-sm bg-white border w-full px-6 py-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className='flex items-center justify-between truncate'>
                        <div>
                          <span className="text-sm font-medium capitalize" dangerouslySetInnerHTML={{ __html: item.description }}></span>
                        </div>
                        {expandedItem === item.name ? (
                          <FiMinus className="h-5 w-5 text-gray-500" />
                        ) : (
                          <FiPlus className="h-5 w-5 text-gray-500" />
                        )}
                      </div>

                      {/* Verification Section Inside Button */}
                      {expandedItem === item.name && (
                        <>
                          <div className="mt-6 w-fit">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setShowVerification(!showVerification);
                              }}
                              className="text-auth_bg text-xs font-semibold"
                            >
                              View Required Verification
                            </button>
                          </div>

                          {showVerification && renderVerificationRequirements(item)}
                        </>
                      )}
                    </button>

                    {/* Rest of expanded content */}
                    {expandedItem === item.name && (
                      <div className="mt-4 flex flex-col gap-2 border-t border-gray-100">
                        {item?.services?.map((service) => (
                          <div key={service.id} className='border rounded-sm'>
                            <button
                              onClick={() => handleToggleService(service.name)}
                              className="shadow-sm bg-white w-full px-6 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
                            >
                              <div className='flex flex-col items-start truncate'>
                                <div className="text-sm font-medium capitalize">
                                  {service.name}
                                </div>
                                <div className='text-[10px] text-gray-500 items-start' dangerouslySetInnerHTML={{ __html: service?.description }} />
                              </div>

                              {service?.limits.length > 0 && (
                                <div>
                                  {expandedService === service.name ? (
                                    <IoChevronUp className="h-5 w-5 text-gray-500" />
                                  ) : (
                                    <IoChevronDownOutline className="h-5 w-5 text-gray-500" />
                                  )}
                                </div>
                              )}

                            </button>

                            {/* Service Limits */}
                            {expandedService === service.name && renderServiceLimits(service)}
                          </div>
                        ))}

                        {/* Change Account Type Button */}
                        {item?.is_current ? (

                          <div className='text-[10px] font-semibold text-green-900 bg-green-600/40 rounded-full p-1 px-2 w-fit'>
                            Current Account Type
                          </div>
                        ) : (
                          <button
                            onClick={() => handleSwitchAccountType(item.verifications, item.id)}
                            className="mb-4 flex items-center gap-1 w-fit text-auth_bg text-[10px] bg-auth_bg/40 p-1 px-2 rounded-md font-semibold"
                          >
                            {changingAccountType ? (
                              <ButtonLoader />
                            ) : (
                              <>
                                <IoMdSwitch size={20} className='font-semibold' />
                                Switch To This Account Type
                              </>
                            )}
                          </button>
                        )}

                      </div>
                    )}
                  </div>

                </div>
              </div>

            ))}
          </div>
        </div >

      )}
    </>

  );
};

export default AccountType