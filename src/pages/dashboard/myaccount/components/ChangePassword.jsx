import Button from '../../../../components/button/button'
import Input from '../../../../components/input/input'
import React, { useState } from 'react'
import toast from 'react-hot-toast'
import Modal from '../../../../components/modal/Modal'
import useModal from '../../../../hooks/useModal'
import { ButtonLoader } from '../../../../utils/ButtonLoader'
import { useMutation } from '@tanstack/react-query'
import { updateAccount } from '../../../../../apis/account';


import { AiOutlineEyeInvisible, AiOutlineEye } from "react-icons/ai";
import { useNavigate } from 'react-router-dom'
import { useCallModal } from '../../../../hooks/useCallModal'
import ModalCP from './SecuritysettingModal/ModalCP'

const ChangePassword = () => {
  const {openModal, isOpenModal, closeModal} = useCallModal()
  
  // password toggles
  const [togglePasswords, setTogglePasswords] = useState({
    pass1: false,
    pass2: false,
    pass3: false
  })

  const handleTogglePasswords = (key) => {
    setTogglePasswords(prev => ({...prev, [key]: !prev[key]}))
  }
  const [confirmnewpassword, setconfirmnewpassword] = useState("")

  // change password states and logic
  const [password, setPassword] = useState({
    old: "",
    new: ""
  })

  const { mutate, isPending } = useMutation({
    mutationFn: updateAccount,
    onSuccess: (data) => {
      console.log("data", data)
      toast.success(`${data?.detail}`)
      openModal()
    },
    onError: (error) => {
      console.log("error", error)
      toast.error(error?.detail)
    }
  })

  const handleChangePassword = (e) => {
    e.preventDefault()
    if(password?.new !== confirmnewpassword) {
      toast.error("Confirm Password does not match with New Password field")
      return;
    }
    mutate({password})
  }

  const check = !password?.new || !password?.old || !confirmnewpassword || isPending;

  return (
    <div className="shadow-sm border rounded-lg border-border_color py-6">

      <div className='md:leading-6 px-6'>
        <div className='font-[500] text-[16px]'>Password Settings</div>
        <div className='font-[300] text-[12px]'>Enter Current Password to update your password</div>
      </div>

      <hr className='h-1 w-full text-border_color my-4' />

      <form action="" onSubmit={handleChangePassword} className='px-6'>

        <div className='flex flex-col my-5'>
          <label htmlFor="password" className='mb-1 font-[500] text-[14px] text-general_text'>Current Password</label>
          <div className='flex items-center pr-2 border rounded-[8px]'>
            <Input id={"password"} placeholder={"Enter Current Password"} type={togglePasswords?.pass1 ? "text" : "password"} className={"p-3 border-none"} value={password?.old} onChange={(e) => setPassword(prev => ({...prev, old: e.target.value }))} />
            <div onClick={()=>handleTogglePasswords("pass1")} className='cursor-pointer'>
              {togglePasswords.pass1 ?
                <AiOutlineEye size={20} />
                :
                <AiOutlineEyeInvisible size={20} />
              }
            </div>
          </div>
        </div>

        <div className='flex flex-col my-5'>
          <label htmlFor="newpassword" className='mb-1 font-[500] text-[14px] text-general_text'>New Password</label>
          <div className='flex items-center pr-2 border rounded-[8px]'>
            <Input id={"newpassword"} placeholder={"Enter New Password"} type={togglePasswords?.pass2 ? "text" : "password"} className={"p-3 border-none"} value={password?.new} onChange={(e) => setPassword(prev => ({...prev, new: e.target.value }))} />
            <div onClick={()=>handleTogglePasswords("pass2")} className='cursor-pointer'>
              {togglePasswords?.pass2 ?
                <AiOutlineEye size={20} />
                :
                <AiOutlineEyeInvisible size={20} />
              }
            </div>
          </div>
        </div>

        <div className='flex flex-col my-5'>
          <label htmlFor="confirmnewpassword" className='mb-1 font-[500] text-[14px] text-general_text'>Confirm New Password</label>
          <div className='flex items-center pr-2 border rounded-[8px]'>
            <Input id={"confirmnewpassword"} placeholder={"Confirm New Password"} type={togglePasswords?.pass3 ? "text" : "password"} className={"p-3 border-none"} value={confirmnewpassword} onChange={(e) => setconfirmnewpassword(e.target.value)} />
            <div onClick={()=> handleTogglePasswords("pass3")} className='cursor-pointer'>
              {togglePasswords?.pass3 ?
                <AiOutlineEye size={20} />
                :
                <AiOutlineEyeInvisible size={20} />
              }
            </div>
          </div>
        </div>

        <div className='flex items-center justify-end mt-10'>
          <div>
            <Button disabled={check} type='submit' className={`p-3 px-12 text-xs ${check ? 'btn_opacity' : ''}`}>
              {isPending ?
              <ButtonLoader />
              :
              <span>Save Changes</span>
            }
            </Button>
          </div>
        </div>

      </form>

      {isOpenModal && (<ModalCP/>)}
    </div>
  )
}

export default ChangePassword;
