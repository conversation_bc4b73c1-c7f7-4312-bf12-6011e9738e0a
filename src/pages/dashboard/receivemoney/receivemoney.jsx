import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getAllCurrencies } from '../../../../apis/currency';
import { ButtonLoader } from '../../../utils/ButtonLoader';
import { Moneywithbanktransfer } from './components/moneywithbanktransfer';
import Input from '../../../components/input/input';
import { BiSearch } from 'react-icons/bi';
import { useCallModal } from '../../../hooks/useCallModal';

const Receivemoney = () => {
  const { data: allCurrencyQuery, isPending: isCurrencyFetching } = useQuery({
    queryKey: ["getallcurrencies", { allow_fund_wallet_with_bank_transfer: true }],
    queryFn: () => getAllCurrencies({ allow_fund_wallet_with_bank_transfer: true }),
  });

  const [selectedCurrency, setSelectedCurrency] = useState("");

  const { isOpenModal, openModal, closeModal } = useCallModal();
  // Handle card click to set selected currency and bank info
  const handleCardClick = (currencyCode) => {
    setSelectedCurrency(currencyCode);
    openModal(); // Open the modal
  };

  const [searchQuery, setSearchQuery] = useState("");  

  const filteredCurrency = allCurrencyQuery?.results?.filter((item) =>
    item?.code?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item?.name?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <>
      <div>
        <div className="flex">
          <div className="flex items-center justify-center border rounded-[8px] mb-4 p-4 py-3 sm:max-w-[50%] w-full">
            <BiSearch className="text-gray-500" />
            <Input
              placeholder={"Search for services"}
              type="search"
              className="border-0 pl-2"
              value={searchQuery}
              onChange={(e)=> setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        {isCurrencyFetching ? (
          <ButtonLoader />
        ) : (
          <div className="shadow-sm border rounded-lg border-border_color p-6 overflow-hidden">
            <div className="grid grid-cols-1 sm:grid-cols-4 gap-8">
              {filteredCurrency?.map((item) => (
                <div
                  key={item?.id}
                  onClick={() => handleCardClick(item?.code)} // On click, set the selected currency and open modal
                  className="cursor-pointer hover:bg-gray-100 transition-all duration-500 ease-in-out flex items-center gap-2 sm:gap-4 border rounded-md p-1 sm:p-3 sm:px-4"
                >
                  <div>
                    {!item?.image ? (
                      <img src={"/images/logo/faviconblue.png"} className='w-[40px] h-[40px] rounded-full' alt="img" />
                    ) : (
                      <img src={item?.image} className='w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] rounded-full' alt="img" />
                    )}
                  </div>
                  <div className="flex flex-col justify-center gap-1">
                    <div className='font-semibold text-xs sm:text-sm truncate'>{item?.name}</div>
                    <div className='text-xs'>{item?.code}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

      </div>
      {/* Only show modal if selected bank info exists */}
      {isOpenModal && (
        <Moneywithbanktransfer selectedCurrency={selectedCurrency} closeModal={closeModal} />
      )
      }
    </>
  );
};

export default Receivemoney;
