import { CiCircleAlert } from "react-icons/ci"
import Modal from "../../../../components/modal/Modal"
import { QRCode } from "antd"
import { useState } from "react"
import { MdContentCopy } from "react-icons/md"
import { HiMiniChevronDown } from "react-icons/hi2";
import { IoMdShareAlt } from "react-icons/io";
import { useQuery } from "@tanstack/react-query"
import { getOrCreateWallet } from "../../../../../apis/wallet"
import { CopyToClipBoard } from "../../../../utils/copyToClipBoard"
import { PageLoader } from "../../../../utils/ButtonLoader"



export const Receivecrypto = ({ closeModal, selectedCurrency, selectedNetwork }) => {

  const { data: walletDetailQuery, isPending } = useQuery({
    queryKey: ["getOrCreateWallet", selectedNetwork],
    queryFn: () => getOrCreateWallet("ETH", { network_type: selectedNetwork })
  })

  console.log("wal", walletDetailQuery)

  const { handleCopyToClipBoardFunction } = CopyToClipBoard({ successMessage: "Wallet Address copied" })

  const handleShare = (account) => {
    const shareableText = `Deposit Address: ${account.address}\nNetwork: ${selectedNetwork}`;

    if (navigator.share) {
      // Use Web Share API if supported
      navigator
        .share({
          title: `${selectedCurrency} Detail`,
          text: shareableText,
        })
        .then(() => console.log('Shared successfully'))
        .catch((error) => console.error('Error sharing:', error));
    } else {
      // Fallback to copying to clipboard
      handleCopyToClipBoardFunction(shareableText);
    }
  };

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title={<>Receive {selectedCurrency} </>}
      >
        {isPending ? (
          <PageLoader />
        ) : (
          <div>
            <div className='text-xs text-lightblack flex items-center text-center leading-5'>
              Receive any BTC amount into any of the below wallet and your account will be credited automatically.
              Note: Charges applies
            </div>

            {walletDetailQuery?.crypto_wallet_addresses?.map((detail) => (
              <div>

                <div className="my-4 border rounded-lg overflow-hidden text-sm">
                  <div className="bg-deepblue flex items-center p-2 px-6 w-full">
                    <span className="text-white uppercase text-[10px] font-medium">
                      QR CODE
                    </span>
                  </div>

                  <div className='p-6'>

                    <div className="flex items-center justify-center py-2 w-full">
                      <QRCode bordered={false} size={200} value={detail?.address || '-'} />
                    </div>
                  </div>

                </div>

                <div className="my-4 border rounded-lg overflow-hidden text-sm">
                  <div className="bg-deepblue flex items-center p-2 px-6 w-full">
                    <span className="text-white uppercase text-[10px] font-medium">
                      Deposit Address
                    </span>
                  </div>

                  <div className='px-4'>

                    <div onClick={() => handleCopyToClipBoardFunction(detail?.address)} className="cursor-pointer capitalize flex gap-4 items-center justify-between py-2 w-full">
                      <span className="text-xs text-lightblack truncate">{detail?.address}</span>
                      <span className='text-[10px] cursor-pointer bg-gray-200 p-3 py-[2px] rounded-lg'>copy</span>
                    </div>
                  </div>

                </div>

                <div className="my-4 border rounded-lg overflow-hidden text-sm">
                  <div className="bg-deepblue flex items-center p-2 px-6 w-full">
                    <span className="text-white uppercase text-[10px] font-medium">
                      Network
                    </span>
                  </div>

                  <div className='px-4'>

                    <div className="capitalize flex items-center justify-between py-2 w-full">
                      <span className="text-xs text-lightblack">{selectedNetwork}</span>
                      {/* <span className='text-xs'>copy</span> */}
                    </div>
                  </div>

                </div>



                <div className="p-4 flex items-start gap-3 rounded-md border border-[#FDE047] bg-[#FEFCE8] text-[#A16207]">
                  <CiCircleAlert size={40} />
                  <span className="text-[10px] font-thin">
                    Please ensure that the contract information of the crypto you deposit is the same as this crypto. Depositing crypto with a different contract information will result in lost assets.
                  </span>
                </div>


                <div className="w-full flex gap-2 items-center justify-center py-4">

                  {/* <span className="p-5 py-2 rounded-md bg-[#F3F4F4] hover:bg-auth_bg text-auth_bg hover:text-white transition-all duration-500 cursor-pointer text-xs">
                    <span className="flex items-center gap-1 text-xs">
                      <HiMiniChevronDown /> Less Details
                    </span>
                  </span> */}

                  <span onClick={() => handleShare(detail)} className="p-5 py-2 rounded-md bg-auth_bg text-white transition-all duration-500 cursor-pointer text-xs">
                    <span className="flex items-center gap-1 text-xs">
                      <IoMdShareAlt /> Share
                    </span>
                  </span>

                </div>

              </div>

            ))}

          </div>
        )}


      </Modal>
    </>
  )
}