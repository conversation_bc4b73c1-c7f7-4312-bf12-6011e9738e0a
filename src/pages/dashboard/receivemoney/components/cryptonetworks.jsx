import { useState } from "react"
import Input from "../../../../components/input/input"
import Modal from "../../../../components/modal/Modal"
import Button from "../../../../components/button/button"
import { useQuery } from "@tanstack/react-query"
import { currencycode<PERSON>pi } from "../../../../../apis/currency"
import { ButtonLoader } from "../../../../utils/ButtonLoader"
import { BiSearch } from "react-icons/bi"
import { useCallModal } from "../../../../hooks/useCallModal"
import { Receivecrypto } from "./receivecrypto"
import { GoCircle } from "react-icons/go"
import { HiMiniCheckCircle } from "react-icons/hi2"

export const Cryptonetworks = ({ closeModal, selectedCurrency }) => {

  console.log("sc", selectedCurrency)
  const [selectedNetwork, setSelectedNetwork] = useState(null)

  const handleSelectNetwork = (network) => {
    // If the same option is clicked again, deselect it
    setSelectedNetwork(selectedNetwork === network ? null : network)
  }

  const { data: networklist, isPending: isLoadingNetwork } = useQuery({
    queryKey: ["getallcryptonetworks", selectedCurrency],
    queryFn: () => currencycodeApi(selectedCurrency),
  });

  console.log("list", networklist)
  console.log("net", selectedNetwork)

  const [searchQuery, setSearchQuery] = useState("");  // State for the search query

  const filteredNetworkName = networklist?.crypto_network_list?.filter((item) =>
    item.network_name.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  const { isOpenModal, openModal, closeModal: closeQRModal } = useCallModal();

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Select Network"
      >
        <div>
          {isLoadingNetwork ? (
            <ButtonLoader />
          ) : networklist?.crypto_network_list?.length < 1 ? (
            <div className='text-xs text-lightblack flex items-center text-center leading-5'>
              There are no available network for this currency.
            </div>
          ) : (
            <div className="flex flex-col gap-4 sm:p-8">

              <div className="flex items-center justify-center border rounded-[8px] p-2 w-full">
                <BiSearch className="text-gray-500" />
                <Input
                  placeholder={"Search for network"}
                  type="search"
                  value={searchQuery} // Bind the input value to the searchQuery state
                  onChange={(e) => setSearchQuery(e.target.value)} // Update state when input changes
                  className="border-0 pl-2"
                />
              </div>

              {filteredNetworkName.map((network) => (
                <div
                  onClick={() => handleSelectNetwork(network?.network_name)}
                  className={`cursor-pointer border ${selectedNetwork === network?.network_name && 'border-auth_bg bg-gray-100'} w-full rounded-xl px-4 p-2 flex items-center justify-between`}
                >
                  <div className="flex flex-col w-[70%]">
                    <div className="font-semibold text-sm capitalize">{network?.network_name}</div>
                    
                    {/* <div className="flex flex-col pt-2">
                      {Object.entries(network?.more_info || {}).map(([key, value]) => (
                        <div key={key} className="text-xs text-gray-500 flex items-center gap-2">
                          <span>{key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}:</span>
                          <span>{value}</span>
                        </div>
                      ))}
                    </div> */}

                  </div>
                  <div className="flex items-center">

                    {selectedNetwork === network?.network_name ? (
                      <HiMiniCheckCircle className="text-auth_bg" />
                    ) : (
                      <GoCircle />
                    )}

                  </div>


                </div>
              ))}


              <div className="mt-4">
                <Button
                  disabled={selectedNetwork === null}
                  onClick={() => openModal()}
                  className="p-3 btn flex items-center justify-center gap-2 font-md text-xs"
                >
                  Proceed
                </Button>
              </div>

            </div>

          )}

        </div>
      </Modal>

      {isOpenModal && (
        <Receivecrypto selectedCurrency={selectedCurrency} selectedNetwork={selectedNetwork} closeModal={closeQRModal} />
      )}
    </>
  )
}