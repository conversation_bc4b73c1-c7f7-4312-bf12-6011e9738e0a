import { useState } from "react"
import Button from "../../../../components/button/button"
import Modal from "../../../../components/modal/Modal"
import { useNavigate } from "react-router-dom"
import { GoCircle } from "react-icons/go"
import { HiMiniCheckCircle } from "react-icons/hi2"

export const Receivemoneycurrencytype = ({ closeModal }) => {
  const navigate = useNavigate()
  const [selectedCurrency, setSelectedCurrency] = useState(null)

  const handleSelect = (currency) => {
    // If the same option is clicked again, deselect it
    setSelectedCurrency(selectedCurrency === currency ? null : currency)
  }

  const handleProceed = async () => {
    try {
      if (selectedCurrency === "local") {
        navigate("/dashboard/receivemoney")
      } else if (selectedCurrency === "crypto") {
        navigate("/dashboard/receivemoney/crypto")
      }
    } catch (error) {
      console.error('Error:', error);
      // Handle any errors here (e.g., show a toast, alert, etc.)
    }
  };

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Select Currency Type"
      >
        <div>
          <div className="flex flex-col gap-4 sm:p-8">

            <div
              onClick={() => handleSelect('local')}
              className={`cursor-pointer border ${selectedCurrency === 'local' && 'border-auth_bg bg-gray-100'} w-full rounded-xl p-6 py-3 flex items-center justify-between`}
            >
              <div className="flex flex-col w-[70%]">
                <div className="font-medium">Local Currency</div>
                <div className="text-xs text-gray-500 mt-2">
                  Receive money into a virtual bank account
                </div>
              </div>
              <div>
                {selectedCurrency === 'local' ? (
                  <HiMiniCheckCircle className="text-auth_bg" />
                ) : (
                  <GoCircle />
                )}
              </div>
            </div>

            <div
              onClick={() => handleSelect('crypto')}
              className={`cursor-pointer border ${selectedCurrency === 'crypto' && 'border-auth_bg bg-gray-100'} w-full rounded-xl p-6 py-3 flex items-center justify-between`}
            >
              <div className="flex flex-col w-[70%]">
                <div className="font-medium">Foreign Currency</div>
                <div className="text-xs text-gray-500 mt-2">
                  Receive crypto currency into acrypto wallet address generated for you
                </div>
              </div>
              <div>

                {selectedCurrency === 'crypto' ? (
                  <HiMiniCheckCircle className="text-auth_bg" />
                ) : (
                  <GoCircle />
                )}

              </div>
            </div>

            <div className="mt-4">
              <Button
                onClick={handleProceed}
                disabled={selectedCurrency === null}
                className="p-3 btn flex items-center justify-center gap-2 font-md text-xs"
              >
                Proceed
              </Button>
            </div>

          </div>
        </div>
      </Modal>
    </>
  )
}