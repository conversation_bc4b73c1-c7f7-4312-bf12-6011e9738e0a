import React from 'react';
import { CopyToClipBoard } from '../../../../utils/copyToClipBoard';
import Modal from '../../../../components/modal/Modal';
import { useQuery } from '@tanstack/react-query';
import { bankAccount } from '../../../../../apis/account';
import { MdContentCopy } from 'react-icons/md';
import { IoIosShareAlt } from 'react-icons/io';
import { ButtonLoader } from '../../../../utils/ButtonLoader';

export const Moneywithbanktransfer = ({ selectedCurrency, closeModal }) => {
  const { data: bankData, isFetching: isBankFetching, isError } = useQuery({
    queryKey: ['bankAccount', { currency_code: selectedCurrency }],
    queryFn: () => bankAccount({ currency_code: selectedCurrency }),
    enabled: !!selectedCurrency, // Only fetch data if currency is selected
  });

  const { handleCopyToClipBoardFunction } = CopyToClipBoard({
    successMessage: 'Account details copied',
  });

  // Function to handle sharing account details
  const handleShare = (account) => {
    const shareableText = `Bank Name: ${account.bank_name}\nAccount Number: ${account.account_number}\nAccount Name: ${account.account_name}`;

    if (navigator.share) {
      // Use Web Share API if supported
      navigator
        .share({
          title: 'Bank Account Details',
          text: shareableText,
        })
        .then(() => console.log('Shared successfully'))
        .catch((error) => console.error('Error sharing:', error));
    } else {
      // Fallback to copying to clipboard
      handleCopyToClipBoardFunction(shareableText);
    }
  };

  // Function to handle copying account details
  const handleCopy = (account) => {
    const copyText = `Bank Name: ${account.bank_name}\nAccount Number: ${account.account_number}\nAccount Name: ${account.account_name}`;
    handleCopyToClipBoardFunction(copyText);
  };

  if (isError) return <div>Error loading bank data</div>;

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[500px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Account Details"
      >
        <div>
          {isBankFetching ? (
            <ButtonLoader />
          ) : bankData?.count === 0 ? (
            <div className="text-xs text-lightblack flex items-center text-center leading-5">
              There are no available bank accounts for this currency.
            </div>
          ) : (
            <div>
              <div className="text-xs text-lightblack flex items-center text-center leading-5">
                Transfer any amount to one of the wallets below, and your account
                will be automatically credited in local currency.
              </div>
              {bankData?.results?.map((account, index) => (
                <div key={index} className="my-4 border rounded-lg overflow-hidden text-sm">
                  <div className="bg-deepblue flex items-center p-2 px-6 w-full">
                    <span className="text-white uppercase text-[10px] font-medium">
                      {account?.bank_name}
                    </span>
                  </div>
                  {/* Loop through account properties */}
                  <div className="px-4">
                    {Object.entries(account).map(([key, value]) => {
                      if (
                        value === null ||
                        ['id', 'currency_symbol', 'bank_code', 'bank_name'].includes(key)
                      ) {
                        return null; // Skip unnecessary properties
                      }
                      return (
                        <div key={key} className="capitalize flex items-center justify-between py-2 w-full">
                          <span className="text-xs text-lightblack">{key.replace(/_/g, ' ')}</span>
                          <span className="text-xs">{value}</span>
                        </div>
                      );
                    })}

                    <div className="w-full flex gap-2 items-center justify-end pb-2">
                      {/* Share Button */}
                      <span
                        className="p-3 py-1 rounded-md bg-[#F3F4F4] hover:bg-auth_bg text-auth_bg hover:text-white transition-all duration-500 cursor-pointer text-xs"
                        onClick={() => handleShare(account)}
                      >
                        <span className="flex items-center gap-1 text-xs">
                          <IoIosShareAlt /> Share
                        </span>
                      </span>

                      {/* Copy Button */}
                      <span
                        className="p-3 py-1 rounded-md bg-[#F3F4F4] hover:bg-auth_bg text-auth_bg hover:text-white transition-all duration-500 cursor-pointer text-xs"
                        onClick={() => handleCopy(account)}
                      >
                        <span className="flex items-center gap-1 text-xs">
                          <MdContentCopy /> Copy
                        </span>
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Modal>
    </>
  );
};