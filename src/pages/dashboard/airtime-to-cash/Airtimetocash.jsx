import React, { useEffect, useState } from 'react'
import { LuPencil } from "react-icons/lu";
import { useMutation, useQuery } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import Input from '../../../components/input/input';
import Button from '../../../components/button/button';
import { getAccount } from '../../../../apis/account';
import { DashboardHeaderContent } from '../../../utils/Utils';
import { getListOfProvidersAirtimeToCash, requestAirtimeToCash } from '../../../../apis/billpayment';
import { ButtonLoader, PageLoader } from '../../../utils/ButtonLoader';
import { BiCopy } from 'react-icons/bi';
import { CopyToClipBoard } from '../../../utils/copyToClipBoard';
import Navigateback from '../../../components/navigateback/navigateback';

const Airtimetocash = () => {

  const { data: getListOfProviders, isPending: isGettingProviders } = useQuery({ queryFn: getListOfProvidersAirtimeToCash, queryKey: ["getListOfProviders"] })
  const { mutateAsync: airtimeToCashMutation, isPending: isRequesting } = useMutation({ mutationFn: requestAirtimeToCash, queryKey: ["airtime-to-cash"] })

  const [payload, setPayload] = useState({
    from_phone_number: "",
    save_as_beneficiary: true
  })

  const handleRequestAirtimeToCash = async (e) => {
    e.preventDefault();
    try {
      const response = await airtimeToCashMutation(payload)
      setPayload({
        from_phone_number: "",
      })
      toast.success(response?.detail || "Airtime Request Successful")
    } catch (error) {
      // Error handling is done in mutation config
    }

  }

  const { handleCopyToClipBoardFunction } = CopyToClipBoard({ successMessage: "Phone number copied" })


  return (
    <div>
      <Navigateback />

      <div className="shadow-sm border rounded-lg border-border_color p-6">
        <DashboardHeaderContent
          header={"Airtime to cash"}
          subheader={"This page allows you convert their airtime into cash from your registered phone Number"}
          headerClassName="font-semibold text-lg"
        />

        <hr className='h-1 w-full text-border_color mt-4' />

        <div className="mt-5 bg-white p-5 px-10 border rounded-[10px] shadow-md overflow-scroll">
          <DashboardHeaderContent
            header={"How it Works"}
            subheader={"Convert your Airtime  to cash in with just a few simple steps"}
            headerClassName="font-medium text-lg"
          />

          <div className="flex gap-6 overflow-x-auto no-scrollbar">
            {getListOfProviders?.airtime_to_cash_settings?.how_it_works?.map((item, index) => (
              <div key={index} className={`w-[2800px] relative bg-white cursor-pointer shadow-xl flex flex-col gap-3 p-8 rounded-[8px]`}>
                <div>
                  <div className="flex h-10 w-10 items-center justify-center p-2 rounded-full bg-auth_bg opacity-75 text-white">
                    {index + 1}
                  </div>
                </div>
                <div className="font-bold text-sm capitalize">{item?.title}</div>
                <div className="text-xs text-general_text" dangerouslySetInnerHTML={{ __html: item?.text }}></div>
              </div>

            ))}

          </div>

        </div>

        <div className="mt-6 bg-white p-5 px-10 border rounded-[10px] shadow-md overflow-scroll">
          <DashboardHeaderContent
            header={"Send Airtime Only to the Number listed Below"}
            headerClassName="font-medium text-lg"
          />

          <div className='flex flex-col gap-3'>

            {getListOfProviders?.airtime_to_cash_settings?.phone_numbers?.map((item, index) => (
              <div key={index} className='flex flex-col gap-1'>
                <div className='text-xs uppercase'>{item?.network}</div>
                <div className='bg-gray-100 rounded-md p-2 flex items-center justify-between'>
                  <div className='text-general_gray_text text-xs'>{item?.phone_number}</div>
                  <div onClick={() => handleCopyToClipBoardFunction(item?.phone_number)} className='cursor-pointer rounded-lg bg-black px-4 p-2 w-fit text-white flex items-center gap-2 text-xs'>
                    <BiCopy />
                    Copy
                  </div>
                </div>
              </div>

            ))}

          </div>
        </div>

        <div className="mt-6 bg-white p-5 px-10 border rounded-[10px] shadow-md overflow-scroll">

          <div className='mb-1'>
            <div className='font-semibold'>Enter Phone Number</div>
            <span className='text-xs'>Enter the phone number you are sending the airtime from</span>
          </div>

          <form onSubmit={handleRequestAirtimeToCash} className="flex flex-col sm:flex-row items-center gap-4">
            <Input
              value={payload.from_phone_number}
              onChange={(e) => setPayload({ ...payload, from_phone_number: e.target.value })}
              type='number'
              placeholder={"Phone Number"}
              className={"p-3"} 
              onInput={(e) => { e.target.value = e.target.value.slice(0, 11); }}
              pattern="\d{0,11}"

            />
            <div className='sm:w-fit w-full'>

              <Button disabled={isRequesting || payload?.from_phone_number?.length < 1} type="submit" className={`p-3 text-xs px-12`}>
                {isRequesting ? (
                  <ButtonLoader />
                ) : (
                  <span>Save</span>
                )}

              </Button>
            </div>
          </form>

        </div>
      </div>
    </div>
  )
}

export default Airtimetocash;
