import React from 'react';
import { useNavigate } from 'react-router-dom';
import Modal from '../../../components/modal/Modal';
import Button from '../../../components/button/button';

const Swapsuccessful = ({response}) => {
  const navigate = useNavigate();
  return (
    <Modal
      width="w-[90%] sm:max-w-[450px]"
      position="modal-center"
      title="Swap Sussessfully"
      showCloseButton={false}
    >
      <div className='flex flex-col'>
        <div className="text-center">
          <div className='py-5 flex items-center justify-center'>
            <img alt='img' src="/images/vectors/success.gif" width={100} height={100} />
          </div>

          <p className='text-[14px] font-[500]'>Swap Successfully</p>
          <p className='text-xs font-[300]'>You have received {response?.swap_to?.code} {parseFloat(response?.swap_to_amount).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})} from your swap transaction on Kompat</p>

          <div className='mt-10'>
            <Button
              onClick={() => navigate("/dashboard")}
              className={"w-full p-3 btn flex items-center justify-center gap-2 font-md text-xs"}
            >
              <span>Back to Dashboard</span>
            </Button>
          </div>

        </div>
      </div>
    </Modal>
  )
}

export default Swapsuccessful
