import { useLocation, useNavigate } from "react-router-dom";
import { IoIosArrowRoundBack } from "react-icons/io";
import { TfiControlForward } from "react-icons/tfi";
import Navigateback from "../../../components/navigateback/navigateback";
import { DashboardHeaderContent } from "../../../utils/Utils";
import Button from "../../../components/button/button";
import { useCallModal } from "../../../hooks/useCallModal";
import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { PaymentMethod } from "../../../components/payment/PaymentMethod";
import { currencyExchangeApi } from "../../../../apis/transactions";
import Swapsuccessful from "./Swapsuccessful";
import useServiceStore from "../../../store/useServiceStore";


export const Currencyexchangesummary = () => {
  const {state} = useLocation();

  console.log("allstates", state)
  
  const navigate = useNavigate()

  const handleNavigateback = (e) => {
    e.preventDefault()
    navigate(-1);
  }

  const { openModal, isOpenModal, closeModal } = useCallModal();
  const { openModal:openSwapSuccess, isOpenModal: isOpenSwapSuccess } = useCallModal();

  // console.log("response", {formData, validatePayload, validateData})

  // const continuation = {formData, validatePayload, validateData}
  // console.log("continue", continuation)
 
  const { mutateAsync, isPending:isCurrencyExchangePending, data:exchangeResponse } = useMutation({
    mutationKey: ["currencyexchange"],
    mutationFn: currencyExchangeApi,
  });

  const { selectedService } = useServiceStore();


  const handleCurrencyExchange = async (transactionPin) => {

    const serviceSlug = "swap";

    const apiPayload = {
      swap_from: state?.swapfrom?.currency?.code,
      swap_to: state?.swapto?.code,
      amount: state?.amount,
      transaction_pin: transactionPin,
      service_id: selectedService?.id || serviceSlug,
    }
    console.log("api", apiPayload)
    try {
      const response = await mutateAsync(apiPayload)
      console.log("myresponse", response);
      toast.success("Your Convertion was successful");
      openSwapSuccess()
      // navigate(`/dashboard/transactions/${response?.id}`)
    } catch (error) {
      console.log("error", error);
      toast.error(error.detail || "An error occurred");
    }
  }


  return (


    <div>
      <Navigateback />
      <DashboardHeaderContent header="Currency Exchange Summary" />

      <div className='w-full md:w-[70%] space-y-10 md:mt-10 capitalize'>
        <div className="grid grid-cols-5 gap-y-2 text-sm">
          <div className="col-span-5 md:col-span-2">
            <span>Swap From:</span>
          </div>
          <div className="col-span-5 md:col-span-3 text-lightblack">
            <div className='w-full'>
              <span>{state?.swapfrom?.currency?.code}</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-5 gap-y-2 text-sm">
          <div className="col-span-5 md:col-span-2">
            <span>Swap To:</span>
          </div>
          <div className="col-span-5 md:col-span-3 text-lightblack">
            <div className='w-full'>
              <span>{state?.swapto?.code}</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-5 gap-y-2 text-sm">
          <div className="col-span-5 md:col-span-2">
            <span>Amount:</span>
          </div>
          <div className="col-span-5 md:col-span-3 text-lightblack">
            <div className='w-full'>
              <span>{state?.amount}</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-5 gap-y-2 text-sm">
          <div className="col-span-5 md:col-span-2">
            <span>Amount you will get:</span>
          </div>
          <div className="col-span-5 md:col-span-3 text-lightblack">
            <div className='w-full'>
              <span>{parseFloat(state?.amount_you_will_get).toLocaleString()}</span>
            </div>
          </div>
        </div>



        <div className="flex items-center gap-4">
          <div>
            <Button
              type='Submit'
              onClick={handleNavigateback}
              variant='transparent'
              className={"p-3 px-6 flex items-center justify-center gap-2 text-xs "}
            >
              <IoIosArrowRoundBack className='text-lg'/>
              <span>Back</span>
            </Button>
          </div>

          <div>
            <Button
              type='Submit'
              onClick={()=>openModal()}
              className={"p-3 px-6 btn flex items-center justify-center gap-2 font-md text-xs"}
            >
              <span>Proceed</span>
              <TfiControlForward className='text-lg'/>
            </Button>
          </div>

        </div>

      </div>

      {isOpenModal && (<PaymentMethod walletBal={state?.swapfrom} closeModal={closeModal} continuation={handleCurrencyExchange} isPending={isCurrencyExchangePending}/>)}
      {isOpenSwapSuccess && (<Swapsuccessful response={exchangeResponse}/>)}


    </div>
  )
}