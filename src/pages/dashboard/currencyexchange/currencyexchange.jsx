import { useSearchParams } from 'react-router-dom';
import { Select } from 'antd';
import Button from '../../../components/button/button';
import Input from '../../../components/input/input';
import Navigateback from '../../../components/navigateback/navigateback';
import { DashboardHeaderContent } from '../../../utils/Utils';
import React, { useEffect, useState } from 'react';
import { exchangeRate, getAllCurrencies } from '../../../../apis/currency';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { getWallet } from '../../../../apis/wallet';
import toast from 'react-hot-toast';
import { PageLoader } from '../../../utils/ButtonLoader';

const Currencyexchange = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const swapToQueryParam = searchParams.get('swapTo');
  const swapFromQueryParam = searchParams.get('swapFrom');

  // State for selected currencies and amount
  const [swapFrom, setSwapFrom] = useState(null);
  const [swapTo, setSwapTo] = useState(null);
  const [swapDetail, setSwapDetail] = useState({
    swapFrom: null,
    swapTo: null,
  });
  const [rate, setRate] = useState(null);
  const [amountPayload, setAmountPayload] = useState({
    enteredAmount: "",
  });

// Fetch wallet data (swapFrom options)
  const { data: allCurrencyQuery, isPending: isCurrencyFetching } = useQuery({
    queryKey: ["getwallet"],
    queryFn: getWallet,
  });

  // Fetch currencies for "swap to" options
  const { data: swapToCurrencies, isPending: isSwapToFetching } = useQuery({
    queryKey: ["getallcurrencies_swapto"],
    queryFn: () => getAllCurrencies({ allow_swap_to: true }),
  });

  // Mutation for fetching exchange rate
  const { mutateAsync: callExchangerate, isPending: isCallingExchangeRate } = useMutation({
    mutationFn: exchangeRate,
    onSuccess: (data) => {
      setRate(data.rate);
    },
    onError: (error) => {
      toast.error(error?.detail || "Failed to fetch exchange rate");
      console.error("Error fetching rate:", error);
    }
  });

  // Prepare options for dropdowns
  const swapFromOptions = React.useMemo(() => {
    return allCurrencyQuery?.results?.map(wallet => ({
      id: wallet.id,
      value: wallet.id,
      label: `${wallet?.currency?.name || 'Unknown'} (${wallet?.currency?.code || 'N/A'})`,
      code: wallet?.currency?.code,
      name: wallet?.currency?.name,
      symbol: wallet?.currency?.symbol,
      balance: wallet?.balance || 0,
      currency: wallet?.currency
    })) || [];
  }, [allCurrencyQuery]);

  const swapToOptions = React.useMemo(() => {
    return swapToCurrencies?.results?.map(currency => ({
      id: currency.id,
      value: currency.id,
      label: `${currency.name || 'Unknown'} (${currency.code || 'N/A'})`,
      code: currency.code,
      name: currency.name,
      symbol: currency.symbol,
      balance: 0
    })) || [];
  }, [swapToCurrencies]);

  // Set the default values based on query parameters
  useEffect(() => {
    // Handle swapTo parameter
    if (swapToQueryParam && swapToOptions.length > 0) {
      const selectedCurrency = swapToOptions.find(
        (currency) => currency.code === swapToQueryParam
      );
      if (selectedCurrency) {
        setSwapTo(selectedCurrency.id);
        setSwapDetail((prev) => ({
          ...prev,
          swapTo: selectedCurrency,
        }));
      }
    }

    // Handle swapFrom parameter
    if (swapFromQueryParam && swapFromOptions.length > 0) {
      const selectedCurrency = swapFromOptions.find(
        (wallet) => wallet.currency?.code === swapFromQueryParam
      );
      if (selectedCurrency) {
        setSwapFrom(selectedCurrency.id);
        setSwapDetail((prev) => ({
          ...prev,
          swapFrom: selectedCurrency,
        }));
      }
    }
  }, [swapToQueryParam, swapToOptions, swapFromQueryParam, swapFromOptions]);

  // Update exchange rate when both currencies are selected
  useEffect(() => {
    if (swapDetail.swapFrom?.currency?.code && swapDetail.swapTo?.code) {
      updateExchangeRate(swapDetail.swapFrom.currency.code, swapDetail.swapTo.code);
    }
  }, [swapDetail.swapFrom, swapDetail.swapTo]);

  // Handle currency selection
  const handleSwapSelection = (value, type) => {
    const options = type === 'swapFrom' ? swapFromOptions : swapToOptions;
    const selectedCurrency = options.find((item) => item.id === value);

    if (selectedCurrency) {
      if (type === 'swapFrom') {
        setSwapFrom(value);
        setSwapDetail((prev) => ({
          ...prev,
          swapFrom: selectedCurrency,
        }));

        // Update URL with the selected swapFrom currency code
        const newParams = new URLSearchParams(searchParams);
        if (selectedCurrency.currency?.code) {
          newParams.set('swapFrom', selectedCurrency.currency.code);
        }
        if (swapDetail.swapTo?.code) {
          newParams.set('swapTo', swapDetail.swapTo.code);
        }
        setSearchParams(newParams);
      } else {
        setSwapTo(value);
        setSwapDetail((prev) => ({
          ...prev,
          swapTo: selectedCurrency,
        }));

        // Update URL with the selected swapTo currency code
        const newParams = new URLSearchParams(searchParams);
        if (selectedCurrency.code) {
          newParams.set('swapTo', selectedCurrency.code);
        }
        if (swapDetail.swapFrom?.currency?.code) {
          newParams.set('swapFrom', swapDetail.swapFrom.currency.code);
        }
        setSearchParams(newParams);
      }
    }
  };

  // Fetch and update exchange rate
  const updateExchangeRate = (from, to) => {
    if (!from || !to) return;

    callExchangerate({
      exchange_from: from,
      exchange_to: to,
      amount: "1"
    });
  };

  // Handle amount input change
  const handleAmountChange = (e) => {
    const value = e.target.value;
    // Ensure only positive numbers
    if (value === '' || parseFloat(value) >= 0) {
      setAmountPayload({ enteredAmount: value });
    }
  };

  // Calculate amount to receive
  const calculateAmountToReceive = () => {
    if (!rate || !amountPayload.enteredAmount) return "";

    const amount = parseFloat(amountPayload.enteredAmount);
    const convertedAmount = amount * parseFloat(rate);

    return isNaN(convertedAmount) ? "" : convertedAmount.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  // Handle form submission
  const handleProceed = (e) => {
    e.preventDefault();

    // Validate form
    if (!swapDetail.swapFrom || !swapDetail.swapTo || !amountPayload.enteredAmount) {
      toast.error("Please fill in all fields");
      return;
    }

    // Check if amount is valid
    const amount = parseFloat(amountPayload.enteredAmount);
    if (isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }

    // Check if user has sufficient balance
    const balance = parseFloat(swapDetail.swapFrom.balance);
    if (amount > balance) {
      toast.error("Insufficient balance");
      return;
    }

    // Navigate to summary page
    navigate("/dashboard/currencyexchange/summary", {
      state: {
        swapfrom: swapDetail.swapFrom,
        swapto: swapDetail.swapTo,
        amount: amountPayload.enteredAmount,
        amount_you_will_get: rate * amountPayload.enteredAmount,
        rate: rate
      },
    });
  };

  return (
    <div>
      <Navigateback />
      <DashboardHeaderContent
        header="Currency Exchange"
        subheader="Exchange any amount between your available currencies below"
      />

      {(isCurrencyFetching || isSwapToFetching) ? (
        <PageLoader />
      ) : (
        <div className="shadow-sm border rounded-lg border-border_color p-6">
          <form onSubmit={handleProceed}>
            {/* Swap From Section */}
            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-12 md:col-span-4">
                <span>Swap From (Select Currency)</span>
              </div>
              <div className="col-span-12 md:col-span-8">
                <Select
                  size="large"
                  className="w-full md:w-[500px] placeholder:text-[12px]"
                  showSearch
                  placeholder="Select Currency"
                  optionFilterProp="label"
                  value={swapFrom}
                  onChange={(value) => handleSwapSelection(value, 'swapFrom')}
                  options={swapFromOptions}
                  loading={isCurrencyFetching}
                />
                {swapFrom && (
                  <div className="text-[12px] text-general_gray_text">
                    Balance: {swapDetail.swapFrom?.symbol}
                    {parseFloat(swapDetail.swapFrom?.balance).toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    })}
                  </div>
                )}
              </div>
            </div>

            {/* Exchange Rate Section */}
            <div className="grid grid-cols-12 gap-6 my-6">
              <div className="col-span-12 md:col-span-4" />
              <div className="col-span-12 md:col-span-8">
                <div className="flex items-center gap-6">
                  <div>
                    <img src="/images/vectors/swap2.png" alt="swap" width="16" height="40" />
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[14px]">
                      Rate: 1 {swapDetail.swapFrom?.currency?.code || ""} ={" "}
                      {rate
                        ? ` ${parseFloat(rate).toLocaleString(undefined, {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        })} ${swapDetail.swapTo?.code}`
                        : isCallingExchangeRate
                          ? " Fetching..."
                          : ""}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Swap To Section */}
            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-12 md:col-span-4">
                <span>Swap To (Select Currency)</span>
              </div>
              <div className="col-span-12 md:col-span-8">
                <Select
                  size="large"
                  className="w-full md:w-[500px] placeholder:text-[12px]"
                  showSearch
                  placeholder="Select Currency"
                  optionFilterProp="label"
                  value={swapTo}
                  onChange={(value) => handleSwapSelection(value, 'swapTo')}
                  options={swapToOptions}
                  loading={isSwapToFetching}
                />
                {swapTo && swapDetail.swapTo?.balance > 0 && (
                  <div className="text-[12px] text-general_gray_text">
                    Balance: {swapDetail.swapTo?.symbol}
                    {parseFloat(swapDetail.swapTo?.balance).toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    })}
                  </div>
                )}
              </div>
            </div>

            <hr className="h-1 w-full text-border_color my-4" />

            {/* Enter Amount Section */}
            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-12 md:col-span-4">
                <span>Enter Amount</span>
              </div>
              <div className="col-span-12 md:col-span-8">
                <div className="w-full md:w-[500px]">
                  <Input
                    value={amountPayload.enteredAmount}
                    onChange={handleAmountChange}
                    name="enteredAmount"
                    type="number"
                    placeholder="Enter Amount"
                    className="p-3"
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>
            </div>

            <hr className="h-1 w-full text-border_color my-4" />

            {/* You Will Get Section */}
            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-12 md:col-span-4">
                <span>You will Get</span>
              </div>
              <div className="col-span-12 md:col-span-8">
                <div className="w-full md:w-[500px]">
                  <Input
                    disabled
                    value={calculateAmountToReceive()}
                    type="text"
                    className="p-3"
                  />
                </div>
              </div>
            </div>

            <hr className="h-1 w-full text-border_color my-4" />

            {/* Continue Button */}
            <div className="flex items-center justify-center">
              <div>
                <Button
                  type="submit"
                  className="w-fit p-3 px-16 text-xs"
                  disabled={!swapDetail.swapFrom || !swapDetail.swapTo || !amountPayload.enteredAmount || isCallingExchangeRate}
                >
                  Continue
                </Button>
              </div>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default Currencyexchange;