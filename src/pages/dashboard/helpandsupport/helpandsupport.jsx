import { DashboardHeaderContent } from '../../../utils/Utils'
import React, { useState } from 'react'
import Faqs from './components/Faqs'
import Navigateback from '../../../components/navigateback/navigateback';


const Helpandsupport = () => {

  const [switchAccountInfo, setSwitchAccountInfo] = useState("faqs")

  const handleSwitch = (type) => {
    setSwitchAccountInfo(type)
  }

  return (
    
    <div>
      <Navigateback />

      <DashboardHeaderContent header="My Account" subheader="Manage and Edit your account information" />

      <div className="shadow-sm border rounded-lg border-border_color p-6">

        <div className='flex items-center md:gap-x-6 border-b-1 border-0'>
          <div className={`flex items-center justify-center hover:text-auth_bg py-2 font-[500] text-[14px] cursor-pointer ${switchAccountInfo === "faqs" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
            onClick={() => handleSwitch("faqs")}>
            <div>FAQs</div>
          </div>

          <div className={`flex items-center hover:text-auth_bg justify-center py-2 font-[500] text-[14px] cursor-pointer ${switchAccountInfo === "livechat" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
            onClick={() => handleSwitch("livechat")}>
            <div>Live Chat</div>
          </div>

          <div className={`flex items-center hover:text-auth_bg justify-center py-2 font-[500] text-[14px] cursor-pointer ${switchAccountInfo === "openticket" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
            onClick={() => handleSwitch("openticket")}>
            <div>Open Ticket</div>
          </div>

          <div className={`flex items-center hover:text-auth_bg justify-center py-2 font-[500] text-[14px] cursor-pointer ${switchAccountInfo === "scamawareness" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
            onClick={() => handleSwitch("scamawareness")}>
            <div>Scam Awareness</div>
          </div>

        </div>

        {switchAccountInfo === "faqs" ?
          <Faqs />
          :
          <Faqs />
        }

      </div>

    </div>
  )
}

export default Helpandsupport;