import { DashboardHeaderContent } from '../../../../utils/Utils'
import React from 'react'
import { CiSearch } from "react-icons/ci";
import { HiOutlinePlus } from "react-icons/hi";


const Faqs = () => {
  return (
    <div>

      <DashboardHeaderContent header={"FAQs"} subheader={"View all our frequently asked questions"} />

      <hr className='h-1 w-full text-border_color my-4' />

      <div className='flex items-center justify-center'>
        <div className='w-full md:w-[500px]'>
          <div className="p-3 border rounded-[8px] text-[#404850] text-[14px] flex items-center gap-2">
            <CiSearch />
            <input type="search" placeholder='Search Frequently Asked Question' className='w-full border-none outline-none focus:ring-0' />
          </div>
        </div>
      </div>

      <hr className='h-1 w-full text-border_color my-4' />

      <div className='grid grid-cols-3'>
        <div className='col-span-1'></div>
        <div className=' col-span-2'>
          <div>
            <div className='flex items-center justify-between border p-4 shadow-md shadow-slate-200'>
              <div>What is Kompat ?</div>
              <HiOutlinePlus />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Faqs
