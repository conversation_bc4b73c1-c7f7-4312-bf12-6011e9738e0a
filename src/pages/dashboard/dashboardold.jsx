import React, { useState } from 'react'
import Input from '../../components/input/input';
import { CiSearch } from "react-icons/ci"
import { IoMdArrowBack, IoMdArrowForward } from "react-icons/io";
import Button from '../../components/button/button';
import { GoArrowUpRight } from "react-icons/go";
import { PiHandbagSimpleLight } from "react-icons/pi";
import RecentTransactions from './components/RecentTransactions';
import { FiEye, FiEyeOff } from "react-icons/fi";
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getAccount } from '../../../apis/account';
import { getWallet } from '../../../apis/wallet';
import { getAllTransaction } from '../../../apis/transactions';
import { IoCopyOutline, IoCheckmarkOutline } from "react-icons/io5";
import { getAllServices } from '../../../apis/settings';
import { DashboardHeaderContent } from '../../utils/Utils';


const Dashboard = () => {

  const frequentData = [
    {
      path: "/images/services/financial.svg",
      name: "Financial Transaction"
    },
    {
      path: "/images/services/virtualcard.svg",
      name: "Virtual Cards"
    },
    {
      path: "/images/services/cart.svg",
      name: "Online Shopping"
    },
    {
      path: "/images/services/education.svg",
      name: "Education"
    },
    {
      path: "/images/services/billpayment.svg",
      name: "Bills Payment"
    },
    {
      path: "/images/services/ride.svg",
      name: "Ride Ordering"
    },
  ]

  const { data: accountQuery, isPending } = useQuery({ queryKey: ["getaccount"], queryFn: getAccount })

  const { data: getWalletQuery } = useQuery({ queryKey: ["getwallet"], queryFn: getWallet })

  const [toggleAmount, setToggleAmount] = useState("")
  const [currentIndex, setCurrentIndex] = useState(0);
  const handleToggleAmount = () => setToggleAmount(!toggleAmount)

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % getWalletQuery?.results.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? getWalletQuery?.results.length - 1 : prevIndex - 1
    );
  };

  const { data: transactionQuery } = useQuery({ queryKey: ["getalltransactions"], queryFn: getAllTransaction })
  const { data: servicesQuery } = useQuery({ queryKey: ["getallservices"], queryFn: getAllServices })


  const [copied, setCopied] = useState(false)

  const copyToClipBoard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopied(true),
        setTimeout(() => setCopied(false), 3000)
    })
  }

  return (
    <div>
      <div className='mb-4 flex flex-col md:flex-row items-center justify-between'>
        <DashboardHeaderContent header="Welcome, Segun Peters" subheader="This overview provides a comprehensive snapshot of general information over time." />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-6 gap-6">

        <div className="md:col-span-4">

          {/* wallet section */}
          <div className="w-full border rounded-md  md:px-4 p-3">
            <div className='flex items-center justify-between'>
              <div className='text-[18px] font-[500]'>My Wallets</div>
              {getWalletQuery?.count > 1 && (
                <div className='flex items-center gap-3'>
                  <span onClick={prevSlide} className='rounded-full border p-2 flex items-center justify-center text-auth_bg cursor-pointer hover:bg-auth_bg hover:text-text_auth transition duration-150'>
                    <IoMdArrowBack size={20} />
                  </span>

                  <span onClick={nextSlide} className='rounded-full border p-2 flex items-center justify-center text-auth_bg cursor-pointer hover:bg-auth_bg hover:text-text_auth transition duration-150'>
                    <IoMdArrowForward size={20} />
                  </span>
                </div>
              )}
            </div>
            <div className='mt-3 relative w-full min-h-[150px] rounded-[10px] overflow-hidden' style={{ background: "linear-gradient(92.74deg, #5C52E0 2.45%, #09007B 98.1%)" }}>
              {getWalletQuery?.results?.map((wallet, index) => (
                <div
                  key={index}
                  className={`absolute inset-0 transition-opacity duration-500 ${index === currentIndex ? 'opacity-100' : 'opacity-0'}`}
                >
                  <div className='p-8'>

                    <div className='flex flex-col text-white gap-4'>

                      <div className='flex items-center gap-2'>
                        <div className='overflow-hidden .rounded-[10px]'>
                          <img className='object-cover' width={30} height={30} src={!wallet.currency?.country?.flag ? "/images/logo/faviconpink.png" : wallet.currency?.country?.flag} alt='img' />
                        </div>
                        <div className='text-lg'>{wallet?.currency?.code}</div>
                      </div>

                      <div className='flex items-center gap-4'>
                        <div className='text-xl font-bold text-[15px] cursor-pointer' onClick={handleToggleAmount}>
                          {toggleAmount ?
                            <span>{wallet?.currency?.symbol + " " + parseFloat(wallet?.balance)?.toLocaleString()}</span>
                            : <span>*****</span>
                          }

                        </div>
                        <div className='cursor-pointer' onClick={handleToggleAmount}>
                          {toggleAmount ?
                            <FiEye size={20} className='cursor-pointer' /> : <FiEyeOff size={20} className='cursor-pointer' />
                          }
                        </div>
                      </div>
                    </div>

                  </div>
                  {!wallet.currency?.country?.flag ? (
                    <img className="hidden sm:block absolute right-0 bottom-0 .top-1" src={"/images/vectors/walletabsolute.svg"} width={200} height={100} alt='img' />

                  ) : (
                    <img className="hidden sm:block absolute right-0 bottom-0 rounded-ss-lg" src={wallet.currency?.country?.flag} width={150} height={100} alt='img' />

                  )}
                </div>
              ))}
            </div>
          </div>



          <div className="flex items-center gap-3 mt-4 overflow-x-auto no-scrollbar">

            <Link to={"/dashboard/wallet"} className='w-full'>
              <Button className={"bg-[#010738] p-3 text-sm flex items-center justify-center gap-2 font-[300] whitespace-nowrap"}> <GoArrowUpRight size={22} /> Add a New Wallet</Button>
            </Link>

            <Link to={"/dashboard/wallet"} className='w-full'>
              <Button className={"bg-[#010738] p-3 text-sm flex items-center justify-center gap-2 font-[300] whitespace-nowrap"}> <GoArrowUpRight size={22} /> Fund this Wallet</Button>
            </Link>

            <Link to={"/dashboard/transactions"} className='w-full'>
              <Button className={"bg-[#010738] p-3 text-sm flex items-center justify-center gap-2 font-[300] whitespace-nowrap"}> <GoArrowUpRight size={22} /> View this Wallet transaction</Button>
            </Link>
          </div>

          <div className="border mt-4 p-2 md:p-4 rounded-md">
            <div className='text-[18px] font-[500]'>Frequetly used services</div>
            <div className='flex items-center gap-4 overflow-x-auto mt-6 no-scrollbar'>

              {frequentData.map((item, index) => (
                <div key={index} className='flex flex-col items-center justify-center'>
                  <div className='flex items-center justify-center p-3 rounded-[12px] bg-[#EBECF5]'>
                    <img src={item.path} width={25} height={25} objectfit='contain' alt={item.name} />
                  </div>
                  <div className='font-[400] text-[14px] mt-2'>{item.name}</div>
                </div>
              ))}

            </div>
          </div>

          {servicesQuery?.count > 0 && (
            <div className="border mt-4 rounded-md p-2 md:p-4">

              <div className='flex items-center justify-between pb-6'>
                <div className='text-[18px] font-[500]'>Apps / Services</div>
                <div className='text-[14px] text-auth_bg cursor-pointer'>
                  <Link to='/dashboard/services'>
                    See All
                  </Link>
                </div>
              </div>

              <div className='grid md:grid-cols-4 gap-3'>

                {servicesQuery?.results?.slice(0, 7).map((service) => (
                  <div key={service.id} className="rounded-[10px] p-4 bg-[#EBECF5]">
                    <div className='py-4 '>
                      {!service?.icon ?
                        <PiHandbagSimpleLight size={30} />
                        :
                        <img src={service?.icon} width={30} height={30} alt='img' />
                      }
                    </div>
                    <div className='font-[500] text-[14px] pb-1 capitalize'>{service?.name}</div>
                    <div className='text-[12px] leading-5 font-[300]'>{service?.description}</div>
                  </div>
                ))}

                <div className=" cursor-pointer rounded-[10px] p-4 bg-[#EBECF5] flex items-center justify-center flex-col gap-4">
                  <div className='font-[500] text-[16px] pb-1'>View More</div>
                  <Link to={"/dashboard/services"} className='rounded-full border border-[#6A05CE] p-1 flex items-center justify-center text-[#6A05CE]'>
                    <IoMdArrowForward size={20} />
                  </Link>
                </div>



              </div>

            </div>
          )}


        </div>

        <div className="md:col-span-2">

          <div className="border p-4 py-6 rounded-md">

            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-3'>

                <div className='relative h-12 w-12 rounded-full overflow-hidden'>
                  <img src={!accountQuery?.profile_picture ? "/images/default.png" : accountQuery?.profile_picture} alt='img' className='w-full h-full object-cover'/>
                </div>
                <div className='capitalize'>{accountQuery?.full_name}</div>
              </div>
              <div className='text-auth_bg text-[14px]'>
                <Link to="/dashboard/myaccount">
                  Go to Profile
                </Link>
              </div>
            </div>

            <div className='flex items-center justify-between mt-6'>

              <div>
                <div className='text-[12px] font-[400] text-general_gray_text'>Account Number</div>
                <div className='text-[12px] cursor-pointer' onClick={() => copyToClipBoard(accountQuery?.uid)}>{accountQuery?.uid} <span className='ml-2 p-1 inline-flex items-center justify-center rounded-full bg-white border hover:bg-auth_bg text-auth_bg  hover:text-white transition-all duration-500 cursor-pointer' onClick={() => copyToClipBoard(accountQuery?.uid)}>{copied ? <IoCheckmarkOutline /> : <IoCopyOutline />}</span></div>
              </div>

              <div>
                <div className='text-[12px] font-[400] text-general_gray_text'>Account Level</div>
                <div className='text-[12px] capitalize'>{accountQuery?.current_account_type?.name}</div>
              </div>

            </div>

            <div className='flex items-center justify-between mt-6'>

              <div>
                <div className='text-[12px] font-[400] text-general_gray_text'>Email</div>
                <div className='text-[12px]'>{accountQuery?.email}</div>
              </div>

              <div>
                <div className='text-[12px] font-[400] text-general_gray_text'>Phone Number</div>
                <div className='text-[12px] capitalize'>{accountQuery?.phone_number}</div>
              </div>


            </div>
          </div>

          <div className="border mt-4 p-4 rounded-md">

            <div className='flex items-center justify-between'>

              <div className='text-[15px]'>Recent Transaction</div>
              <div className='text-[13px] font-[400] text-auth_bg'>
                <Link to="/dashboard/transactions">
                  See All
                </Link>
              </div>

            </div>

            {transactionQuery?.count <= 0 ? (
              <div className='text-xs mt-4'>No available transaction at the moment</div>
            )
              :
              transactionQuery?.results?.slice(0, 15)?.map((res) => (
                <RecentTransactions key={res?.id} res={res} />
              ))
            }

          </div>

        </div>

      </div>

    </div>
  )
}

export default Dashboard;