import { useEffect, useState } from "react";
import Button from "../../../../../components/button/button"
import Modal from "../../../../../components/modal/Modal"
import { ButtonLoader } from "../../../../../utils/ButtonLoader";
import { TfiControlForward } from "react-icons/tfi";
import PinInput from "react-pin-input";
import { useMutation, useQuery } from "@tanstack/react-query";
import { internalFundTransfer } from "../../../../../../apis/transactions";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";



export const EnterPin = ({ closeModal, continuation }) => {
  const [transactionPin, setTransactionPin] = useState("");
  const navigate = useNavigate()

  const handleInputChange = (value) => {
    setTransactionPin(value);
  };

  // Create payment payload as a regular object
  const paymentPayload = {
    currency: continuation?.formData?.currency,
    account_number: continuation?.validateData?.account_number,
    amount: continuation?.formData?.amount,
    pay_with: "wallet",
    description: continuation?.formData?.description,
    transaction_pin: transactionPin,
    source: "web",
    is_beneficiary: true,
  };

  const { mutateAsync, isPending } = useMutation({
    mutationKey: "payment",
    mutationFn: internalFundTransfer,
  });

  const handleFundTransfer = async () => {
    try {
      const response = await mutateAsync(paymentPayload);
      console.log("myresponse", response);
      toast.success("Fund Transfer successful")
      navigate(`/dashboard/transactions/${response?.id}`)
    } catch (error) {
      console.log("error", error);
      toast.error(error.detail || "An error occurred");
    }
  };

  useEffect(() => {
    if (transactionPin.length === 4) {
      handleFundTransfer();
    }
  }, [transactionPin]);

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[500px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Payment Method"
      >
        <div className="overflow-hidden">
          <div className="px-10">
            <div className="overflow-hidden">
              <p className="text-sm text-center mb-6 text-general_gray_text">Please enter your 4 digit PIN</p>

              <div className='mb-[16px] text-center'>
                <PinInput
                  length={4}
                  initialValue=""
                  secret={false}
                  secretDelay={100}
                  onChange={handleInputChange}
                  type="numeric"
                  inputMode="number"
                  style={{ width: "100%" }}
                  inputStyle={{ background: "#E7E7E7", borderRadius: "3px", border: "none", margin: "4px" }}
                  inputFocusStyle={{ border: "1px solid #335ADD" }}
                  onComplete={(value, index) => { }}
                  autoSelect={true}
                  regexCriteria={/^[ A-Za-z0-9_@./#&+-]*$/}
                />
              </div>

              <div className="mt-6">
                <Button
                  type='submit'
                  disabled={transactionPin.length !== 4 || isPending}
                  onClick={handleFundTransfer}
                  className={`p-3 px-6 btn flex items-center justify-center gap-2 font-md text-xs ${transactionPin.length !== 4 || isPending && 'btn-opacity'}`}
                >
                  {isPending ?
                    (
                      <ButtonLoader />
                    ) : (
                      <>
                        <span>Proceed</span>
                        <TfiControlForward className='text-lg' />
                      </>

                    )}

                </Button>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};