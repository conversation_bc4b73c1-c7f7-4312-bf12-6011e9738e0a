import React, { useCallback, useEffect, useState } from 'react';
import { CiSearch } from 'react-icons/ci';
import Button from '../../../../components/button/button';
import { Select } from 'antd';
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query';
import { bankAccount } from '../../../../../apis/account';
import { validateAccountNumber } from '../../../../../apis/settings';
import Input from '../../../../components/input/input';
import { PageLoader } from '../../../../utils/ButtonLoader';
import { HashLoader } from 'react-spinners';
import { useNavigate } from 'react-router-dom';
import debounce from 'lodash.debounce';
import { FormatNumberWithCommas } from '../../../../utils/FormatNumberWithCommas';
import { getWallet } from '../../../../../apis/wallet';

const Internal = () => {
  const navigate = useNavigate();
  const [defaultCurrency, setDefaultCurrency] = useState(null);

  // Get all wallets
  const { data: walletData, isPending: isWalletLoading } = useQuery({
    queryKey: ["getwallet"],
    queryFn: getWallet,
  });

  console.log("waldata", walletData)

  // Get all beneficiaries
  const { data: internalBeneficiary, isPending: loadingBeneficiary } = useQuery({
    queryKey: ['getallbeneficiary'],
    queryFn: () => bankAccount({ is_internal_beneficiary: true }),
  });

  const [searchQuery, setSearchQuery] = useState('');

  // Function to extract initials
  const getInitials = (name) => {
    const names = name.split(' ');
    const firstNameInitial = names[0]?.charAt(0).toUpperCase() || '';
    const lastNameInitial = names[names.length - 1]?.charAt(0).toUpperCase() || '';
    return `${firstNameInitial}${lastNameInitial}`;
  };

  // Filter beneficiaries based on search query
  const filteredBeneficiaries = internalBeneficiary?.results?.filter((item) =>
    item?.account_name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const [validatePayload, setValidatePayload] = useState({
    internal: true,
    account_number: '',
  });

  const {
    mutateAsync,
    data: validateData,
    isPending: isValidating,
    isError: validateError,
  } = useMutation({ mutationFn: validateAccountNumber });

  useEffect(() => {
    const handleValidateAccountNumber = () => {
      mutateAsync({ ...validatePayload });
    };

    if (validatePayload.account_number.length === 10) {
      handleValidateAccountNumber();
    }
  }, [validatePayload, mutateAsync]);

  const [formData, setFormData] = useState({
    currency: '',
    amount: '',
    description: '',
    accountNo: '',
    accountName: '',
    currencySymbol: '',
  });

  // Set default currency when wallet data is loaded
  useEffect(() => {
    if (walletData?.results?.length > 0) {
      const defaultWallet = walletData.results.find(
        wallet => wallet.currency?.is_user_default
      );
      
      if (defaultWallet) {
        setDefaultCurrency(defaultWallet.currency.code);
        handleCurrencyChange(defaultWallet.currency.code);
      }
    }
  }, [walletData]);

  const handleCurrencyChange = (value) => {
    const selectedWallet = walletData?.results?.find(
      wallet => wallet.currency?.code === value
    );
    
    if (selectedWallet) {
      setFormData(prev => ({
        ...prev,
        currency: value,
        currencySymbol: {
          symbol: selectedWallet.currency?.symbol || '',
          balance: selectedWallet.balance || 0,
          code: selectedWallet.currency?.code || '',
          name: selectedWallet.currency?.name || ''
        }
      }));
    }
  };

  const handleAmountChange = (e) => {
    const inputValue = e.target.value;

    // If backspace is pressed and input is empty, allow it
    if (inputValue === '') {
      setFormData(prev => ({ ...prev, amount: '' }));
      return;
    }

    // Remove commas before processing
    const unformattedValue = inputValue.replace(/,/g, '');

    // Check if it's a valid number
    if (!isNaN(unformattedValue)) {
      setFormData(prev => ({ ...prev, amount: unformattedValue }));
    }
  };

  const handleBeneficiaryClick = (accountNumber) => {
    setValidatePayload((prev) => ({
      ...prev,
      account_number: accountNumber,
    }));
  };

  const check =
    !validatePayload?.account_number ||
    !validateData?.account_name ||
    !formData?.currency ||
    !formData?.amount ||
    formData?.amount > formData?.currencySymbol?.balance;

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!check) {
      navigate('/dashboard/sendmoney/transaction-summary', {
        state: { formData, validatePayload, validateData },
      });
    }
  };

  return (
    <>
      {loadingBeneficiary || isWalletLoading ? (
        <div className="flex w-full items-center justify-center text-auth_bg">
          <PageLoader />
        </div>
      ) : (
        <form onSubmit={handleSubmit}>
          <div className="md:leading-6 my-6">
            <div className="font-[500] text-[18px]">Send Money Internally</div>
            <div className="font-[300] text-[13px]">
              Fill in the required Information to send money to Kompat Account.
            </div>
          </div>

          <hr className="h-1 w-full text-border_color my-4" />

          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-12 md:col-span-4">
              <span>Currency</span>
            </div>
            <div className="col-span-12 md:col-span-8">
              <Select
                size="large"
                className="w-full md:w-[500px] placeholder:text-[12px]"
                showSearch
                placeholder="Select Currency"
                optionFilterProp="label"
                autoFocus
                value={formData.currency || defaultCurrency}
                onChange={handleCurrencyChange}
                filterOption={(input, option) =>
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
                options={
                  walletData?.results?.map((wallet) => ({
                    key: wallet.id,
                    value: wallet.currency?.code,
                    label: (
                      <div>
                        <span>{wallet.currency?.name} ({wallet.currency?.code})</span>
                        {wallet.currency?.is_crypto && wallet.network && (
                          <span className="ml-2 border p-2 py-1 text-[8px] capitalize rounded-md bg-gray-100 h-10">
                            {wallet?.network?.network_name}
                          </span>
                        )}
                      </div>
                    ),
                  })) || []
                }
                notFoundContent={
                  isWalletLoading ? (
                    <div className="flex justify-center p-2">
                      <PageLoader size={20} color="#2A4365" />
                    </div>
                  ) : null
                }
              />
              {formData?.currency && (
                <div className="text-[12px] text-general_gray_text">
                  Balance: {`${formData?.currencySymbol?.symbol}${parseFloat(
                    formData?.currencySymbol?.balance
                  ).toLocaleString()}`}
                </div>
              )}
            </div>
          </div>

          {internalBeneficiary?.count > 0 && (
            <hr className="h-1 w-full text-border_color my-4" />
          )}

          {internalBeneficiary?.count > 0 && (
            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-12 md:col-span-4">
                <span>Saved Beneficiaries</span>
              </div>

              <div className="col-span-12 md:col-span-8">
                <div className="w-full md:w-[500px] space-y-4">
                  <div className="p-2 border rounded-[8px] text-[#404850] text-[14px] flex items-center gap-2">
                    <CiSearch />
                    <input
                      type="search"
                      placeholder="Search Beneficiaries"
                      className="w-full border-none outline-none focus:ring-0"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  <div className="p-2 border rounded-[8px] text-[#404850] text-[14px] flex items-center gap-2 overflow-x-scroll no-scrollbar">
                    {filteredBeneficiaries?.map((item) => (
                      <div
                        key={item?.id}
                        className="p-2 hover:bg-gray-100 transition-all duration-500 ease-in-out rounded-md flex items-center justify-center flex-col gap-2 cursor-pointer"
                        onClick={() => handleBeneficiaryClick(item.account_number)}
                      >
                        <div className="w-10 h-10 rounded-full bg-[#EBECF5] flex items-center justify-center">
                          {getInitials(item?.account_name)}
                        </div>
                        <div className="text-[8px] whitespace-nowrap">
                          {item?.account_name.slice(0, 15)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          <hr className="h-1 w-full text-border_color my-4" />

          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-12 md:col-span-4">
              <span>Account Number</span>
            </div>
            <div className="col-span-12 md:col-span-8">
              <div className="w-full md:w-[500px]">
                <Input
                  onInput={(e) => {
                    e.target.value = e.target.value.slice(0, 10);
                  }}
                  value={validatePayload?.account_number}
                  onChange={(e) =>
                    setValidatePayload((prev) => ({
                      ...prev,
                      account_number: e.target.value,
                    }))
                  }
                  type="number"
                  placeholder="Enter Account Number"
                  className="p-3"
                />
                {isValidating && (
                  <div className="fixed inset-0 w-full h-full bg-black bg-opacity-50 flex justify-center items-center z-[10] transition-all duration-1500 ease-in-out">
                    <div className="flex items-center justify-center gap-4">
                      <HashLoader size={20} />
                    </div>
                  </div>
                )}

                {validateError && (
                  <span className="flex justify-start my-2 mt-1 font-[400] text-sm text-red-500">
                    Account name could not be fetched
                  </span>
                )}
              </div>
            </div>
          </div>

          {validateData && (
            <hr className="h-1 w-full text-border_color my-4" />
          )}

          {validateData && (
            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-12 md:col-span-4">
                <span>Account Name</span>
              </div>
              <div className="col-span-12 md:col-span-8">
                <div className="w-full md:w-[500px]">
                  <Input
                    type="text"
                    disabled={true}
                    value={validateData?.account_name}
                    className="p-3"
                  />
                </div>
              </div>
            </div>
          )}

          <hr className="h-1 w-full text-border_color my-4" />

          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-12 md:col-span-4">
              <span>Amount</span>
            </div>
            <div className="col-span-12 md:col-span-8">
              <div className="w-full md:w-[500px]">
                <Input
                  type="text"
                  value={formData.amount ? FormatNumberWithCommas(formData.amount) : ''}
                  onChange={handleAmountChange}
                  placeholder="Enter Amount"
                  className="p-3"
                  inputMode="decimal"
                  min="0"
                />
              </div>
            </div>
          </div>

          <hr className="h-1 w-full text-border_color my-4" />

          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-12 md:col-span-4">
              <span>Narration</span>
            </div>
            <div className="col-span-12 md:col-span-8">
              <div className="w-full md:w-[500px]">
                <Input
                  type="text"
                  value={formData?.description}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Enter Description"
                  className="p-3"
                />
              </div>
            </div>
          </div>

          <hr className="h-1 w-full text-border_color my-4" />

          <div className="flex items-center justify-center">
            <div>
              <Button
                type="submit"
                disabled={check}
                className={`p-3 px-16 text-xs ${check && ` btn_opacity`}`}
              >
                Continue
              </Button>
            </div>
          </div>
        </form>
      )}
    </>
  );
};

export default Internal;
