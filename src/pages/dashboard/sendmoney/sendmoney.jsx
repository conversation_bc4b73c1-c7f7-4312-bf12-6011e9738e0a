import Tabparams from '../../../components/Tabparams/Tabparams';
import External from './components/External'
import Internal from './components/Internal'
import { DashboardHeaderContent } from '../../../utils/Utils'
import React from 'react'
import Navigateback from '../../../components/navigateback/navigateback';

const Sendmoney = () => {
  const { handleSwitch, switchInfo } = Tabparams({ defaultTab: "Internal" })

  return (
    <div>
      <Navigateback />
      <DashboardHeaderContent header="Send Money" subheader="Send Money Internally and Externally on Kompat" />

      <div className="shadow-sm border rounded-lg border-border_color p-6 overflow-hidden">

        <div className='flex items-center gap-4 md:gap-x-6 border-b-1 border-0'>
          <div className={`flex items-center justify-center hover:text-auth_bg py-2 font-[500] text-[14px] cursor-pointer ${switchInfo === "Internal" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
            onClick={() => handleSwitch("Internal")}>
            <div>Send Money Internally</div>
          </div>

          <div className={`flex items-center hover:text-auth_bg justify-center py-2 font-[500] text-[14px] cursor-pointer ${switchInfo === "External" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
            onClick={() => handleSwitch("External")}>
            <div>Send Money Externally</div>
          </div>
        </div>

        {switchInfo === "Internal" ?
          <Internal />
          :
          <External />
        }

      </div>
    </div>
  )
}

export default Sendmoney;