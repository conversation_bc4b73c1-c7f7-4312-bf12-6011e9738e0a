"use client"

import React, { useState } from 'react'
import Button from '../../../components/button/button';
import { HiOutlinePlus } from "react-icons/hi";
import { useQuery } from '@tanstack/react-query';
import Tabparams from '../../../components/Tabparams/Tabparams';
import { DashboardHeaderContent } from '../../../utils/Utils';
import Navigateback from '../../../components/navigateback/navigateback';
import { useCallModal } from '../../../hooks/useCallModal';
import Addcard from './components/Addcard';
import { getAllCards } from '../../../../apis/card';
import GenericTable from '../../../components/table/GenericTable';
import { Dropdown, Switch } from 'antd';
import { HiOutlineDotsVertical } from "react-icons/hi";
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { FreezeCard } from '../../../../apis/card';
import toast from 'react-hot-toast';
import Deletecard from './components/cardsmodal/Deletecard';

const Cards = () => {
  const { handleSwitch, switchInfo } = Tabparams({ defaultTab: "virtual" });
  const queryClient = useQueryClient();
  const [selectedCardData, setSelectedCardData] = useState(null);
  const [modalType, setModalType] = useState(null);

  const { data: allCardsQuery, isPending: fetchingCards } = useQuery({
    queryKey: ["getallcards", switchInfo],
    queryFn: () => getAllCards({ is_virtual: switchInfo === "virtual" ? true : false }),
  });

  const { isOpenModal, openModal, closeModal } = useCallModal();

  // Mutation to freeze the card
  const { mutateAsync: freezeCardMutate, isPending: isFreezingCard } = useMutation({
    mutationFn: FreezeCard,
    onSuccess: (data) => {
      toast.success(data?.detail || 'Card status updated successfully');
      queryClient.invalidateQueries(["getallcards"]);
    },
    onError: (error) => {
      toast.error(error?.detail || 'Error updating card status');
    },
  });

  // Handle freezing/unfreezing the card
  const handleFreezeToggle = async (card, checked) => {
    try {
      const payload = {
        id: card.id,
        is_frozen: checked // true = freeze card, false = unfreeze card
      };
      
      setSelectedCardData({...card, id: card.id}); // Set the card being updated
      await freezeCardMutate(payload);
    } catch (error) {
      console.error("Error toggling card freeze status:", error);
    }
  };

  // Open modal and set selected card data and modal type
  const handleOpenModal = (card, type) => {
    setSelectedCardData(card);  // Set the selected card object
    setModalType(type);  // Set modal type (freeze or delete)
    openModal();
  };

  // Add a new function to handle opening the Add Card modal
  const handleAddCardModal = () => {
    setModalType(null);  // Reset modal type to null for Add Card
    setSelectedCardData(null);  // Reset selected card data
    openModal();
  };

  // Define columns for the cards table
  const columns = [
    {
      key: 'name',
      header: 'Card Holder',
    },
    {
      key: 'last_4_digits',
      header: 'Card Number',
      render: (item) => `**** **** **** ${item.last_4_digits}`
    },
    {
      key: 'bank',
      header: 'Bank Name',
      render: (item) => !item.bank ? "--" : item.bank
    },
    {
      key: 'card_type',
      header: 'Card Type',
      render: (item) => <span className="capitalize">{item.card_type}</span>
    },
    {
      key: 'exp_date',
      header: 'Card Expire On',
      render: (item) => `${item.exp_month} / ${item.exp_year}`
    },
    {
      key: 'country_code',
      header: 'Country Code',
    },
    {
      key: 'is_frozen',
      header: 'Freeze Card',
      sortable: false,
      render: (item) => (
        <Switch 
          size="small" 
          checked={item.is_frozen}
          loading={isFreezingCard && selectedCardData?.id === item.id}
          onChange={(checked) => handleFreezeToggle(item, checked)}
          onClick={(e) => e.stopPropagation()}
        />
      )
    },
    {
      key: 'actions',
      header: 'Action',
      sortable: false,
      render: (item) => (
        <Dropdown
          overlayStyle={{ width: "250px" }}
          menu={{
            items: [
              {
                label: <div className='text-xs font-md py-1'>Fund Card</div>,
                key: '0',
              },
              {
                label: <div className='text-xs font-md py-1' onClick={() => handleOpenModal(item, 'delete')}>Remove Card</div>,
                key: '2',
              },
            ],
          }}
          trigger={['click']}
        >
          <a onClick={(e) => e.preventDefault()}>
            <div className='p-1 border border-auth_bg rounded-md w-8 h-8 flex items-center justify-center cursor-pointer'>
              <HiOutlineDotsVertical />
            </div>
          </a>
        </Dropdown>
      )
    }
  ];

  return (
    <>
      <div>
        <Navigateback />

        <div className='flex items-center justify-between'>
          <DashboardHeaderContent header="Cards" subheader="This overview provides a comprehensive snapshot of general information over time." />
          <div>
            <Button
              onClick={handleAddCardModal}  // Use the new function instead of just openModal
              className={"p-3 btn flex items-center justify-center gap-2 font-md text-xs whitespace-nowrap"}
            ><HiOutlinePlus size={16} className='text-white' /> Add New Card</Button>
          </div>
        </div>

        <div className="shadow-sm border rounded-lg border-border_color p-6 px-0">
          <div className='flex items-center gap-4 md:gap-x-6 border-b-1 border-0 px-4'>
            <div className={`flex items-center hover:text-auth_bg justify-center py-2 font-[500] text-[14px] cursor-pointer ${switchInfo === "virtual" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
              onClick={() => handleSwitch("virtual")}>
              <div>Virtual Cards</div>
            </div>

            <div className={`flex items-center justify-center hover:text-auth_bg py-2 font-[500] text-[14px] cursor-pointer ${switchInfo === "physical" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
              onClick={() => handleSwitch("physical")}>
              <div>Physical Cards</div>
            </div>
          </div>

          {allCardsQuery?.count <= 0 ? (
            <div className='text-sm mt-6 px-4'>
              You don't have any {switchInfo === "virtual" ? "virtual" : "physical"} card on your account yet
            </div>
          ) : (
            <GenericTable
              columns={columns}
              data={allCardsQuery}
              isPending={fetchingCards}
              emptyMessage={`No available ${switchInfo} cards at the moment`}
              itemsPerPage={10}
            />
          )}
        </div>
      </div>

      {isOpenModal && !modalType && <Addcard closeModal={closeModal} />}
      {(isOpenModal && modalType) === 'delete' && (
        <Deletecard 
          closeModal={closeModal}
          cardId={selectedCardData} 
          onSuccess={() => {
            queryClient.invalidateQueries(["getallcards"]);
            closeModal();
          }}
        />
      )}
    </>
  )
}

export default Cards;
