import { useMutation, useQuery } from '@tanstack/react-query';
import Modal from '../../../../../components/modal/Modal';
import { addCardApi, cardConfig } from '../../../../../../apis/card';
import Button from '../../../../../components/button/button';
import { DashboardHeaderContent, OnboardingLogo } from '../../../../../utils/Utils';
import { ButtonLoader } from '../../../../../utils/ButtonLoader';
import { useCallModal } from '../../../../../hooks/useCallModal';
import { VirtualSuccessModal } from './VirtualSuccessModal';
import toast from 'react-hot-toast';
import { useNavigate } from 'react-router-dom';

export const Virtualdetail = ({ selectedCurrency, closeModal }) => {
  const navigate = useNavigate();
  const { data: configDetailQuery, isPending } = useQuery({
    queryKey: ['config-card', selectedCurrency],
    queryFn: () => cardConfig({ currency_code: selectedCurrency }),
    enabled: !!selectedCurrency,
  });

  const response = configDetailQuery?.results?.[0] || {}; // Access the first item in the results array

  const accessLocalstorage = JSON.parse(localStorage.getItem("Kompat_userData"));
  const getUser = accessLocalstorage.userData?.full_name

  const { isOpenModal, openModal, closeModal: closeVirtualSuccessModal } = useCallModal()

  const apiPayload = {
    is_virtual: true,
    currency_code: selectedCurrency,
    source: "web"
  };

  const { mutateAsync, isPending: addCardPending } = useMutation({
    mutationKey: ["add-new-card"],
    mutationFn: addCardApi,
  });

  const handleProceed = async (e) => {
    e.preventDefault();

    try {
      const response = await mutateAsync(apiPayload)
      toast.success(response?.detail || `${selectedCurrency} Card added successfully`)
      window.location.href = "/dashboard/cards"
    } catch (error) {

    }
  }

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[600px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
      >
        {isPending ? (
          <ButtonLoader />
        ) : configDetailQuery?.count === 0 ? (
          <div className="text-xs text-lightblack flex items-center text-center leading-5">
            There are no available charges for this currency.
          </div>
        ) : (
          <div>
            <div className='flex items-center justify-center flex-col mx-auto'>
              <div className='relative w-[140px]'>
                <OnboardingLogo path={"/images/logo/logoblue.png"} />
              </div>
              <DashboardHeaderContent
                header={getUser}
                subheader="You are about to add a new virtual card, here is the summary  cost of the virtual card"
                headerClassName={"text-sm font-semibold"}
                subheaderClassName={"text-center"}
                className={"flex justify-center flex-col items-center sm:max-w-[70%]"}
              />

            </div>
            <div className='sm:px-10 pb-4'>
              {Object.entries(response?.display_data).map(([key, value]) => {
                // Skip the "currency" field
                if (["currency", "active"].includes(key) || value === null) {
                  return null;
                }

                console.log(value)
                // Render non-object values
                return (
                  <div key={key} className="flex items-center justify-between py-2 text-xs">
                    <div className="capitalize font-normal">{key.replace(/_/g, ' ')}</div>
                    <div className="font-medium">{value}</div>
                  </div>
                );
              })}

              <Button
              onClick={handleProceed}
                disabled={addCardPending}
                className="p-3 btn flex items-center justify-center gap-2 font-md text-xs mt-4"
              >
                {addCardPending ? (
                  <ButtonLoader />
                ) : (
                  <span>Proceed</span>
                )}
              </Button>
            </div>
          </div>


        )}

      </Modal>

      {isOpenModal && (
        <VirtualSuccessModal closeModal={closeVirtualSuccessModal} />
      )}
    </>
  );
};