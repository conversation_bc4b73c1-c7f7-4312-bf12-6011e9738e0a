import React, { useState } from 'react';
import { Select } from 'antd';
import Button from '../../../../../components/button/button';
import Modal from '../../../../../components/modal/Modal';
import { PageLoader } from '../../../../../utils/ButtonLoader';
import useCurrencySearch from '../../../../../hooks/useCurrencySearch';
import { useCallModal } from '../../../../../hooks/useCallModal';
import { Virtualdetail } from './Virtualdetail';

export const Virtualcurrency = ({ closeModal }) => {

  const additionalParams = {
    is_crypto: false,
  };

  const {
    searchTerm,
    setSearchTerm,
    setIsSearching,
    isSearching,
    allCurrencyInfiniteData,
    isCurrencyFetching,
    isFetchingNextCurrencyPage,
    debouncedSearch,
    handleScroll,
  } = useCurrencySearch('', additionalParams);

  const { openModal, isOpenModal, closeModal: closeVirtualModal } = useCallModal();
  const [selectedCurrency, setSelectedCurrency] = useState('');

  const handleCurrencyChange = (value) => {
    setSelectedCurrency(value); // Update the selected currency
  };

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Select Currency"
      >
        <form className="overflow-hidden">
          <div className="flex flex-col mx-auto justify-center w-full py-8 .md:w-[300px]">
            <div className="text-sm text-start font-[400] py-2">Currency</div>
            <div>
              <Select
                size="large"
                className="text-start w-full .md:w-[300px] placeholder:text-[12px]"
                showSearch
                placeholder="Select Currency"
                optionFilterProp="label"
                autoFocus
                onChange={handleCurrencyChange}
                onSearch={(value) => {
                  setIsSearching(true);
                  debouncedSearch(value);
                }}
                onPopupScroll={handleScroll}
                filterOption={false}
                options={
                  allCurrencyInfiniteData?.pages.flatMap((page) =>
                    page.results.map((item) => ({
                      key: item.id,
                      value: item.code,
                      label: `${item.name} (${item.code})`,
                    }))
                  ) || []
                }
                notFoundContent={
                  isCurrencyFetching || isSearching ? (
                    <div className="flex justify-center p-2">
                      <PageLoader size={20} color="#2A4365" />
                    </div>
                  ) : null
                }
                dropdownRender={(menu) => (
                  <>
                    {menu}
                    {isFetchingNextCurrencyPage && (
                      <div className="flex justify-center p-2">
                        <PageLoader size={20} color="#2A4365" />
                      </div>
                    )}
                  </>
                )}
              />
            </div>

            <div className="mt-10">
              <Button
                onClick={()=> openModal()} // Open the Virtualdetail modal
                className="p-3 btn flex items-center justify-center gap-2 font-md text-xs"
                disabled={!selectedCurrency} // Disable if no currency is selected
              >
                <span>Add Card</span>
              </Button>
            </div>
          </div>
        </form>
      </Modal>

      {isOpenModal && (
        <Virtualdetail selectedCurrency={selectedCurrency} closeModal={closeVirtualModal} />
      )}
    </>
  );
};