import React, { useState } from 'react';
import Modal from '../../../../../components/modal/Modal';
import Button from '../../../../../components/button/button';
import { ButtonLoader } from '../../../../../utils/ButtonLoader';
import { useMutation } from '@tanstack/react-query';
import { Modalsuccess } from './Modalsuccess';
import { useCallModal } from '../../../../../hooks/useCallModal';
import toast from 'react-hot-toast';
import { FreezeCard } from '../../../../../../apis/card';

const Freezecard = ({ closeModal, selectedCardData }) => {
  const { isOpenModal, openModal, closeModal: closeSuccessModal } = useCallModal();

  // State to hold the payload to be sent to the API
  const [payload, setPayload] = useState({
    is_default: false,
    is_frozen: false,
  });

  // Mutation to freeze the card
  const { mutateAsync: freezeCardMutate, isLoading: isFreezingCard } = useMutation({
    mutationFn: FreezeCard,
    onSuccess: (data) => {
      toast.success(data?.detail || 'Card frozen successfully');
      openModal();
    },
    onError: (error) => {
      toast.error(error?.detail || 'Error freezing card');
    },
  });

  // Handle freezing the card
  const handleFreezeCard = async () => {
    const updatedPayload = { ...payload, is_frozen: true };

    setPayload(updatedPayload);

    if (selectedCardData?.id) {
      await freezeCardMutate({ id: selectedCardData?.id, ...updatedPayload });
    }
  };

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Freeze Card"
      >
        <div>
          <p className='text-sm text-center my-10 font-thin'>
            Are you sure you want to freeze <span className='font-medium'>{selectedCardData?.name}</span> card?
          </p>

          <div className="flex flex-col mb-4 items-center gap-2">
            <Button
              onClick={handleFreezeCard}
              disabled={isFreezingCard}
              className={"p-3 btn flex items-center justify-center gap-2 font-md text-xs"}
            >
              {isFreezingCard ? (
                <ButtonLoader />
              ) : (
                <span>Proceed</span>
              )}
            </Button>
          </div>
        </div>
      </Modal>

      {isOpenModal && <Modalsuccess closeModal={closeSuccessModal} />}
    </>
  );
};

export default Freezecard;