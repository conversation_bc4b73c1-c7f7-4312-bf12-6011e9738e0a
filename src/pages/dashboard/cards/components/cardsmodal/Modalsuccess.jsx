import { useNavigate } from "react-router-dom"
import Button from "../../../../../components/button/button"
import Modal from "../../../../../components/modal/Modal"
import { useQueryClient } from "@tanstack/react-query"

export const Modalsuccess = ({closeModal}) => {
  const navigate = useNavigate()
  const queryClient = useQueryClient()

  queryClient.invalidateQueries(["getallcards"])

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        title="Deleted Card"
        showCloseButton={true}
        onClose={closeModal}
      >
        <div className='flex flex-col'>
          <div className="text-center">
            <div className='py-5 flex items-center justify-center'>
              <img alt='img' src="/images/vectors/success.gif" width={100} height={100} />
            </div>

            <p className='text-[14px] font-[500]'>Card Deleted Successfully</p>
            <p className='text-xs font-[300]'>You successfully delete this card.</p>

            <div className='mt-10'>
              <Button
                onClick={() => navigate("/dashboard")}
                className={"w-full p-3 btn flex items-center justify-center gap-2 font-md text-xs"}
              >
                <span>Back to Dashboard</span>
              </Button>
            </div>

          </div>
        </div>
      </Modal>
    </>
  )
}