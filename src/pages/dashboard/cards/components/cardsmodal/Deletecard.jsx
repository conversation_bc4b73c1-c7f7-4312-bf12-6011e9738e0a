import React from 'react'
import Modal from '../../../../../components/modal/Modal'
import Button from '../../../../../components/button/button'
import { ButtonLoader } from '../../../../../utils/ButtonLoader';
import { useMutation } from '@tanstack/react-query';
import { Modalsuccess } from './Modalsuccess';
import { useCallModal } from '../../../../../hooks/useCallModal';
import toast from 'react-hot-toast';
import { deleteCard } from '../../../../../../apis/card';

const Deletecard = ({ closeModal, cardId }) => {
  console.log("cad", cardId)
  const {isOpenModal, openModal, closeModal:closeSuccessModal } = useCallModal();

  const {mutateAsync: deleteCardMutate, isPending: isDeletingCard } = useMutation({
    mutationFn: deleteCard,
    onSuccess: (data) => {
      toast.success(data?.detail || 'Card removed successfully');
      openModal();  // Open the success modal

    },
    onError: (error) => {
      toast.error(error?.detail || 'Error removing card');
    }
  });

  const handleDeleteCard = async () => {
    await deleteCardMutate({ id: cardId?.id });
  }

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Remove Card"
      >

        <div>

          <p className='text-sm text-center my-10 font-thin'>
            Are you sure you want to remove <span className='font-medium'>{cardId?.name}</span> card?
          </p>

          <div className="flex flex-col mb-4 items-center gap-2">

            <Button
              onClick={handleDeleteCard}
              disabled={isDeletingCard}
              className={"p-3 btn flex items-center justify-center gap-2 font-md text-xs"}
            >
              {isDeletingCard ? (
                <ButtonLoader />
              ) : (
                <span>Proceed</span>
              )}
            </Button>

          </div>


        </div>



      </Modal>
      {isOpenModal && <Modalsuccess closeModal={closeSuccessModal}/>}
    </>
  )
}

export default Deletecard
