import React, { useState } from 'react'
import { Select } from 'antd';
import { useCallModal } from '../../../../hooks/useCallModal';
import Modal from '../../../../components/modal/Modal';
import Input from '../../../../components/input/input';
import Button from '../../../../components/button/button';
import { useMutation } from '@tanstack/react-query';
import { ButtonLoader } from '../../../../utils/ButtonLoader';
import { Modalsuccess } from './cardsmodal/Modalsuccess';
import { Virtualcurrency } from './Addvirtual/Virtualcurrency';
import { addCardApi } from '../../../../../apis/card';
import { HiMiniCheckCircle } from 'react-icons/hi2';
import { GoCircle } from 'react-icons/go';

const Addcard = ({ closeModal }) => {

  const { isOpenModal, openModal, closeModal: closeCard } = useCallModal();

  const [selectCardType, setSelectCardType] = useState(null)

  const handleSelect = (type) => {
    // If the same option is clicked again, deselect it
    setSelectCardType(selectCardType === type ? null : type)
  }

  let domain = window.location.host
  let protocol = window.location.protocol
  const callBackUrl = `${protocol}//${domain}/dashboard/cards?tab=physical`


  const apiPayload = {
    redirect_url: callBackUrl,
    is_virtual: selectCardType === 'virtual', // Set based on selected card type
    source: "web"
  };

  const { mutateAsync: addCardMutation, isPending: addCardPending } = useMutation({
    mutationKey: ["add-new-card"],
    mutationFn: addCardApi,
  });


  const handleProceed = async () => {
    if (selectCardType === 'virtual') {
      openModal()
      return
    }

    try {
      // Call the API to add the card
      const response = await addCardMutation(apiPayload);
      if (response && response.payment_link) {
        window.location.href = response.payment_link
      }
    } catch (error) {
      console.error('Error adding card:', error);
      // Handle any errors here (e.g., show a toast, alert, etc.)
    }
  };

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Add New Card"
      >

        <div className='overflow-hidden'>
          <div className='flex flex-col mx-auto justify-center w-full .md:w-[300px]'>
            <div onClick={() => handleSelect("physical")} className='flex items-center justify-between my-4 px-10 cursor-pointer'>

              <div>
                <div className='text-sm font-medium'>Physical Card</div>
                <div className='text-[10px] text-gray-500'>Add New Physical Card.</div>
              </div>

              <div>
                {selectCardType === 'physical' ? (
                  <HiMiniCheckCircle className="text-auth_bg" />
                ) : (
                  <GoCircle />
                )}
              </div>
            </div>

            <hr className='mx-10' />
            <div onClick={() => handleSelect("virtual")} className='flex items-center justify-between my-4 px-10 cursor-pointer'>

              <div>
                <div className='text-sm font-medium'>Virtual Card</div>
                <div className='text-[10px] text-gray-500'>Add New Physical Card.</div>
              </div>

              <div>
                {selectCardType === 'virtual' ? (
                  <HiMiniCheckCircle className="text-auth_bg" />
                ) : (
                  <GoCircle />
                )}
              </div>

            </div>

            <div className='mt-6'>
              <Button
                onClick={handleProceed}
                disabled={selectCardType === null || addCardPending}
                className={"p-3 btn flex items-center justify-center gap-2 font-md text-xs"}
              >
                {addCardPending ? (
                  <ButtonLoader />
                ) : (
                  <span>Proceed</span>
                )}
              </Button>
            </div>
          </div>
        </div>

      </Modal>

      {isOpenModal && <Virtualcurrency closeModal={closeCard} />}
    </>
  )
}

export default Addcard;