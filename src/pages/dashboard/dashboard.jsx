import { useState } from 'react'
import { IoMdArrowBack, IoMdArrowForward } from "react-icons/io";
import Button from '../../components/button/button';
import { GoArrowUpRight } from "react-icons/go";
import { <PERSON>Eye, FiEyeOff } from "react-icons/fi";
import { Link, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getAccount } from '../../../apis/account';
import { getWallet } from '../../../apis/wallet';
import { getAllTransaction } from '../../../apis/transactions';
import { getAllServices } from '../../../apis/settings';
import { DashboardHeaderContent, formatDate } from '../../utils/Utils';
import TransactionTable from './transactions/components/Transactiontable';
import { MdContentCopy } from "react-icons/md";
import { PageLoader } from '../../utils/ButtonLoader';
import { CopyToClipBoard } from '../../utils/copyToClipBoard';
import { useCallModal } from '../../hooks/useCallModal';
import { Receivemoneycurrencytype } from './receivemoney/components/receivemoneycurrencytype';
import { Services } from '../welcome/components/services';
import QuickActions from './components/QuickActions';
import { SlUser } from "react-icons/sl";
import { IoCheckmarkDoneCircle } from "react-icons/io5";
import { RiErrorWarningFill } from "react-icons/ri";
import NotificationBar from '../../components/NotificationBar';

const Dashboard = () => {

  const { data: accountQuery, isPending: isFetchingAccount } = useQuery({ queryKey: ["getaccount"], queryFn: getAccount })

  const { data: getWalletQuery } = useQuery({ queryKey: ["getwallet"], queryFn: getWallet })

  const [toggleAmount, setToggleAmount] = useState("")
  const [currentIndex, setCurrentIndex] = useState(0);
  const [imageLoaded, setImageLoaded] = useState(false);
  const handleToggleAmount = () => setToggleAmount(!toggleAmount)

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % getWalletQuery?.results.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? getWalletQuery?.results.length - 1 : prevIndex - 1
    );
  };

  const { data: transactionQuery, isPending: isTransactionPending } = useQuery({ queryKey: ["getalltransactions"], queryFn: getAllTransaction })
  const { data: servicesQuery } = useQuery({ queryKey: ["getallservices"], queryFn: getAllServices })

  const { handleCopyToClipBoardFunction } = CopyToClipBoard({ successMessage: "Account number copied" })

  const { isOpenModal, openModal, closeModal } = useCallModal();

  const navigate = useNavigate();

  // Check if any verification is pending
  const hasPendingVerifications = accountQuery?.verification?.account_type_verification?.verifications?.some(
    verification => verification.verification_status === "pending"
  );

  console.log("get", getWalletQuery)


  return (
    <>
      {isFetchingAccount ? (
        <div className="w-full min-h-screen flex items-center justify-center">
          <PageLoader />
        </div>
      ) : (

        <div>

          {/* Add NotificationBar if there are pending verifications */}
          {hasPendingVerifications && <NotificationBar />}


          <div className='mb-4 flex flex-col md:flex-row items-center justify-between'>
            <DashboardHeaderContent header={`Welcome, ${accountQuery?.full_name}.`} subheader="This overview provides a comprehensive information of your account." />
          </div>

          <div className="shadow-primary sm:rounded-xl bg-white p-2 sm:p-6">

            {/* wallet section */}
            <div className="grid grid-cols-5 gap-10">
              <div className="md:col-span-3 col-span-5">

                <div className='flex items-center justify-between pb-4 mb-4 border-b'>
                  <div className='text-[18px] font-[500]'>My Wallets</div>
                  {getWalletQuery?.count > 1 && (

                    <div className='flex items-center gap-3'>
                      <span onClick={prevSlide} className='rounded-full border p-1 flex items-center justify-center text-auth_bg cursor-pointer hover:bg-auth_bg hover:text-text_auth transition duration-150'>
                        <IoMdArrowBack size={18} />
                      </span>

                      <span onClick={nextSlide} className='rounded-full border p-1 flex items-center justify-center text-auth_bg cursor-pointer hover:bg-auth_bg hover:text-text_auth transition duration-150'>
                        <IoMdArrowForward size={18} />
                      </span>
                    </div>

                  )}
                </div>
              </div>
            </div>

            <div className='grid grid-cols-5 gap-10'>
              <div className="col-span-5 md:col-span-3">
                <div>
                  <div className="relative w-full h-56 overflow-hidden">
                    {getWalletQuery?.results?.map((wallet, index) => (
                      <div
                        key={index}
                        className={`absolute inset-0 transition-opacity duration-500 ${index === currentIndex ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'}`}
                      >
                        <div className="relative w-full h-full">
                          {!imageLoaded && (
                            <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-2xl" />
                          )}
                          <img
                            src="/wallet-bg.svg"
                            className={`w-full h-full object-cover rounded-2xl transition-opacity duration-300 ${imageLoaded ? 'opacity-100' : 'opacity-0'
                              }`}
                            loading="lazy"
                            alt="Background"
                            onLoad={() => setImageLoaded(true)}
                            onError={() => setImageLoaded(true)}
                          />
                        </div>
                        <div className="absolute top-0 left-10 w-full h-full flex flex-col justify-center gap-6 text-white">
                          <div className="flex items-center gap-2">
                            <img
                              className='rounded-full h-6 w-6'
                              src={!wallet.currency?.image ? "/images/logo/faviconblue.png" : wallet.currency?.image}
                              alt='country'
                            />
                            <div className='font-normal capitalize'>{wallet?.currency?.name}</div>
                          </div>
                          <div className='cursor-pointer font-bold text-3xl flex items-center w-fit' onClick={handleToggleAmount}>
                            {toggleAmount ?
                              <span>{wallet?.currency?.symbol + " " + parseFloat(wallet?.balance)?.toLocaleString()}</span>
                              :
                              <span>**********</span>
                            }
                            <span className='ps-3'>
                              {toggleAmount ?
                                <FiEye size={20} className='cursor-pointer' />
                                :
                                <FiEyeOff size={20} className='cursor-pointer' />
                              }
                            </span>
                          </div>
                          <div
                            className='w-fit'
                            onClick={() => {
                              navigate(`/dashboard/transactions/${wallet?.currency?.code}`);
                            }}
                          >
                            <Button
                            variant='secondary'
                              className={"px-6 md:px-10 p-3 text-xs sm:text-xs rounded-[10px] !bg-black"}
                            >
                              View Wallet Transaction
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="flex items-center justify-between gap-4 mt-5 overflow-x-auto no-scrollbar">
                    <Link to={"/dashboard/wallet"} className='w-full'>
                      <Button className={"p-3 text-xs flex items-center justify-center gap-2 font-[300] whitespace-nowrap rounded-[1px]"}> <GoArrowUpRight size={22} /> View all Wallets</Button>
                    </Link>

                    {/* <Link to={"/dashboard/sendmoney"} className='w-full'>
                      <Button className={"p-3 text-xs flex items-center justify-center gap-2 font-[300] whitespace-nowrap rounded-[1px]"}> <GoArrowUpRight size={22} /> Send Money</Button>
                    </Link> */}

                    <Button onClick={() => openModal()} variant='secondary' className={"p-3 text-xs flex items-center justify-center gap-2 font-[300] whitespace-nowrap rounded-[1px]"}> <GoArrowUpRight size={22} /> Receive Money</Button>

                  </div>

                </div>
              </div>
              <div className="col-span-5 md:col-span-2 hidden sm:block">

                <div className="shadow-custom md:p-4 py-6 rounded-lg overflow-hidden">

                  <div className='flex items-center justify-between'>
                    <div className='flex items-center gap-3'>

                      <div className='relative h-12 w-12 rounded-full overflow-hidden'>
                        {!accountQuery?.profile_picture ? (
                          <div className='w-full h-full flex items-center justify-center bg-gray-200'>
                            <SlUser />
                          </div>
                        ) : (
                          <img src={accountQuery?.profile_picture} alt='img' className='w-full h-full object-cover' />
                        )}
                      </div>
                      <div className='capitalize truncate'>{accountQuery?.full_name}</div>
                      {/* <div className="bg-green-100 rounded-full text-green-800 text-xs p-1 px-4 flex items-center"><span className='w-1 h-1 rounded-full bg-green-900 me-1'></span>Active</div> */}
                    </div>
                    <div className='text-auth_bg text-[10px] md:text-[14px] whitespace-nowrap justify-end'>
                      <Link to="/dashboard/myaccount">
                        Go to Profile
                      </Link>
                    </div>
                  </div>

                  <div className='flex items-center justify-between mt-6 text-[12px]'>

                    <div>
                      <div className='font-[400] text-general_gray_text'>Account Number</div>
                      <div className='cursor-pointer flex items-center' onClick={() => handleCopyToClipBoardFunction(accountQuery?.internal_account_number)}>{accountQuery?.internal_account_number}
                        <span className='ml-2 p-1 px-2 rounded-full bg-[#F2F3FF] hover:bg-auth_bg text-auth_bg  hover:text-white transition-all duration-500 cursor-pointer text-xs'>
                          <span className='flex items-center gap-1'><MdContentCopy /> Copy</span></span>
                      </div>
                    </div>

                    <div className='flex flex-col items-end'>
                      <div className='font-[400] text-general_gray_text'>Account Type</div>
                      <div className='capitalize'>{accountQuery?.current_account_type?.name}</div>
                    </div>

                  </div>

                  <div className='flex items-center justify-between mt-6 text-[12px]'>

                    <div>
                      <div className=' font-[400] text-general_gray_text'>Email</div>
                      <div className=''>{accountQuery?.email}</div>
                    </div>

                    <div className='flex flex-col items-end'>
                      <div className=' font-[400] text-general_gray_text'>Phone Number</div>
                      <div className=' capitalize'>{accountQuery?.phone_number}</div>
                    </div>

                  </div>

                  <div className='flex items-center justify-between mt-6 text-[12px]'>

                    <div>
                      <div className=' font-[400] text-general_gray_text'>Verification Status</div>
                      <div>{accountQuery?.verification?.user_completely_verified ? (
                        <span className='flex items-center gap-1'>Verified <IoCheckmarkDoneCircle className='text-auth_bg' size={16} /></span>
                      ) : (
                        <span className='flex items-center gap-1'>Not Verified <RiErrorWarningFill className='text-red-400' size={16} /></span>
                      )}</div>
                    </div>

                    <div className='flex flex-col items-end'>
                      <div className=' font-[400] text-general_gray_text'>Last Login</div>
                      <div className=''>{formatDate(accountQuery?.last_login)}</div>
                    </div>

                  </div>

                </div>

              </div>
            </div>

            {/* quick action section */}
            <QuickActions />

            {/* recent transactions section */}
            <div className='flex flex-col mt-10 md:p-2 rounded-xl bg-white shadow-primary'>
              <div className="flex items-center justify-between">
                <div className='text-[18px] font-[500]'>Recent Transactions</div>
                <div>
                  <Button onClick={() => navigate("/dashboard/transactions")} variant='transparent' className="w-fit px-4 py-2 text-xs">See All</Button>
                </div>
              </div>

              <TransactionTable transactions={transactionQuery} isPending={isTransactionPending} />

            </div>

            <div className='mt-10 md:py-6 overflow-x-auto flex gap-6 no-scrollbar'>
              {servicesQuery?.results?.map((service) => (

                <div key={service?.slug} className="flex flex-col gap-5 min-w-[250px]">
                  <Services services={service} />
                </div>

              ))}
            </div>


          </div>

        </div>
      )}

      {isOpenModal && (
        <Receivemoneycurrencytype closeModal={closeModal} />
      )}


    </>
  )
}

export default Dashboard;