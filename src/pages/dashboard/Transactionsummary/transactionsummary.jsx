import React, { useState } from 'react'
import { DashboardHeaderContent } from '../../../utils/Utils'
import Navigateback from '../../../components/navigateback/navigateback'
import { useLocation, useNavigate } from 'react-router-dom'
import Button from '../../../components/button/button'
import { ButtonLoader } from '../../../utils/ButtonLoader'
import { IoIosArrowRoundBack } from "react-icons/io";
import { TfiControlForward } from "react-icons/tfi";
import { useCallModal } from '../../../hooks/useCallModal'
import { PaymentMethod } from '../../../components/payment/PaymentMethod'
import { useMutation } from '@tanstack/react-query'
import { internalFundTransfer } from '../../../../apis/transactions'
import toast from 'react-hot-toast'
import { Switch } from 'antd'
import useServiceStore from '../../../store/useServiceStore'

export const Transactionsummary = () => {
  const location = useLocation();
  const { formData, validatePayload, validateData } = location.state || {};

  const navigate = useNavigate()

  const handleNavigateback = (e) => {
    e.preventDefault()
    navigate(-1);
  }
  const { openModal, isOpenModal, closeModal } = useCallModal();

  console.log("response", { formData, validatePayload, validateData })

  const [isBeneficiary, setIsBeneficiary] = useState(false);

  const { mutateAsync, isPending: isSendingMoneyInternalPending } = useMutation({
    mutationKey: ["internalfundtransfer"],
    mutationFn: internalFundTransfer,
  });

  const [paymentMethod, setPaymentMethod] = useState('wallet'); // Track payment method
  const [cardId, setCardId] = useState(null); // Track selected card ID

  const { selectedService } = useServiceStore();


  const handleInternalFundTransfer = async (transactionPin) => {
    const serviceSlug = "internal transfer";

    // Find the internal transfer service
    const basePayload = {
      account_number: validatePayload?.account_number,
      currency: formData?.currency,
      amount: formData?.amount,
      pay_with: paymentMethod,
      narration: formData?.description,
      transaction_pin: transactionPin,
      source: "web",
      is_beneficiary: isBeneficiary,
      service_id: selectedService?.id || serviceSlug, // Will be undefined if service not found
    }

    const apiPayload = paymentMethod === "card" ? {
      ...basePayload,
      card: cardId
    } : {
      ...basePayload,
    }

    console.log("payload", apiPayload)

    try {
      const response = await mutateAsync(apiPayload)
      console.log("myresponse", response);
      toast.success(response?.detail || "Fund Transfer Successful");
      navigate(`/dashboard/transactions/${response?.transaction_type}/${response?.id}/`)
    } catch (error) {
      console.log("error", error);
      toast.error(error.detail || "An error occurred");
    }
  }

  return (


    <div>
      <Navigateback />
      <DashboardHeaderContent header="Transactions Detail" subheader="Review the transaction you are about to do." />

      <div className='w-full md:w-[70%] space-y-10 md:mt-10'>
        <div className="grid grid-cols-5 gap-y-2 text-sm">
          <div className="col-span-5 md:col-span-2">
            <span>Selected Currency:</span>
          </div>
          <div className="col-span-5 md:col-span-3 text-lightblack">
            <div className='w-full'>
              <span>{formData.currency}</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-5 gap-y-2 text-sm">
          <div className="col-span-5 md:col-span-2">
            <span>Account Number:</span>
          </div>
          <div className="col-span-5 md:col-span-3 text-lightblack">
            <div className='w-full'>
              <span>{validatePayload.account_number}</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-5 gap-y-2 text-sm">
          <div className="col-span-5 md:col-span-2">
            <span>Account Name:</span>
          </div>
          <div className="col-span-5 md:col-span-3 text-lightblack">
            <div className='w-full'>
              <span>{validateData?.account_name}</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-5 gap-y-2 text-sm">
          <div className="col-span-5 md:col-span-2">
            <span>Amount:</span>
          </div>
          <div className="col-span-5 md:col-span-3 text-lightblack">
            <div className='w-full'>
              <span>{formData.currencySymbol?.symbol} {parseFloat(formData.amount).toLocaleString()}</span>
            </div>
          </div>
        </div>
        {formData.description && (
          <div className="grid grid-cols-5 gap-y-2 text-sm">
            <div className="col-span-5 md:col-span-2">
              <span>Narration:</span>
            </div>
            <div className="col-span-5 md:col-span-3 text-lightblack">
              <div className='w-full'>
                <span>{formData.description}</span>
              </div>
            </div>
          </div>
        )}

        <div className='flex gap-4 items-center'>
          <span className='text-xs'>
            Save as Beneficiary
          </span>

          <Switch
            size="small"
            checked={isBeneficiary}
            onChange={(checked) => setIsBeneficiary(checked)}
          />
        </div>

        <div className="flex items-center gap-4">
          <div>
            <Button
              type='Submit'
              onClick={handleNavigateback}
              variant='transparent'
              className={"p-3 px-6 flex items-center justify-center gap-2 text-xs text-slate-900"}
            >
              <IoIosArrowRoundBack className='text-lg' />
              <span>Back</span>
            </Button>
          </div>

          <div>
            <Button
              type='Submit'
              onClick={() => openModal()}
              className={"p-3 px-6 btn flex items-center justify-center gap-2 font-md text-xs"}
            >
              <span>Proceed</span>
              <TfiControlForward className='text-lg' />
            </Button>
          </div>

        </div>

      </div>

      {isOpenModal && (
        <PaymentMethod
          walletBal={formData.currencySymbol}
          closeModal={closeModal}
          continuation={handleInternalFundTransfer}
          isPending={isSendingMoneyInternalPending}
          setPaymentMethod={setPaymentMethod} // Pass setPaymentMethod to PaymentMethod
          setCardId={setCardId} // Pass setCardId to PaymentMethod
        />
      )}

    </div>
  )
}
