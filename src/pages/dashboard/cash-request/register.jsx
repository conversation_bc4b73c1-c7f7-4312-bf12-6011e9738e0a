import React from 'react'
import Navigateback from '../../../components/navigateback/navigateback'
import { DashboardHeaderContent } from '../../../utils/Utils'
import { Select } from 'antd'
import Input from '../../../components/input/input'

const Register = () => {
  return (
    <div>
      <Navigateback />
      <DashboardHeaderContent header="Register as our agent" subheader="Register as our agents for delivering cash to people who needs it in their location." />

      <div className="shadow-sm border rounded-lg border-border_color p-6 overflow-hidden">
        <DashboardHeaderContent
          header="Cash Agent"
          subheader="Fill the form to become one of our agents for dispatching cash to customers"
          headerClassName={"font-[400] text-xs"}
          subheaderClassName={"font-light"}
        />

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>State</span>
          </div>
          <div className="col-span-12 md:col-span-8">

            <Select
              size='large'
              className='w-full md:w-[500px] placeholder:text-[12px]'
              showSearch
              placeholder="Select your state"
              optionFilterProp="label"
              autoFocus
              // onChange={handleCurrencyChange}
              onSearch={(value) => (value)}
            // options={
            //   allCurrencyQuery ? allCurrencyQuery?.results.map((item) => ({ key: item?.id, value: item?.code, label: `${item?.name} (${item?.code})` })) : []
            // }
            />
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>LGA</span>
          </div>
          <div className="col-span-12 md:col-span-8">

            <Select
              size='large'
              className='w-full md:w-[500px] placeholder:text-[12px]'
              showSearch
              placeholder="Select your L.G.A"
              optionFilterProp="label"
              autoFocus
              // onChange={handleCurrencyChange}
              onSearch={(value) => (value)}
            // options={
            //   allCurrencyQuery ? allCurrencyQuery?.results.map((item) => ({ key: item?.id, value: item?.code, label: `${item?.name} (${item?.code})` })) : []
            // }
            />
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>Denominations</span>
          </div>
          <div className="col-span-12 md:col-span-8">

            <Select
              size='large'
              className='w-full md:w-[500px] placeholder:text-[12px]'
              showSearch
              placeholder="Select your denomination"
              optionFilterProp="label"
              autoFocus
              // onChange={handleCurrencyChange}
              onSearch={(value) => (value)}
            // options={
            //   allCurrencyQuery ? allCurrencyQuery?.results.map((item) => ({ key: item?.id, value: item?.code, label: `${item?.name} (${item?.code})` })) : []
            // }
            />
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>Percentage</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <div className='w-full md:w-[500px]'>
              <Input
                type='number' placeholder={""} className={"p-3"} />
            </div>
          </div>
        </div>

      </div>

    </div>
  )
}

export default Register
