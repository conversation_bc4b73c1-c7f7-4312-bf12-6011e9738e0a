import Button from '../../../../components/button/button';
import Modal from '../../../../components/modal/Modal';
import { useCallModal } from '../../../../hooks/useCallModal';
import useModal from '../../../../hooks/useModal';
import { ButtonLoader } from '../../../../utils/ButtonLoader';
import { useMutation } from '@tanstack/react-query';
import { Dropdown } from 'antd';
import React, { useState, useCallback, useEffect } from 'react';
import toast from 'react-hot-toast';
import { HiOutlineDotsVertical } from "react-icons/hi";
import Deletecard from './cardsmodal/Deletecard';
import { deleteCard } from '../../../../../apis/card';

const VirtualCards = ({ allTransactionData, refetchCards }) => {
  const [selectedCardId, setSelectedCardId] = useState(null); // Initialize with null
  const [isModalOpen, setIsModalOpen] = useState(false);

  // const {
  //   isOpen,
  //   title,
  //   content,
  //   proceed,
  //   openModal,
  //   closeModal,
  //   onProceed
  // } = useModal();

  const {isOpenModal, openModal, closeModal } = useCallModal();

  const { mutateAsync: deleteCardMutate, isLoading: isDeletingCard } = useMutation({
    mutationFn: deleteCard,
    onSuccess: (data) => {
      toast.success(data?.detail || 'Card removed successfully');
      refetchCards();
      closeModal(); // Close the modal after successful deletion
    },
    onError: (error) => {
      toast.error(error?.detail || 'Error removing card');
    }
  });

  const handleDeleteCard = useCallback(async () => {
    if (selectedCardId) {
      await deleteCardMutate({ id: selectedCardId });
    }
  }, [selectedCardId, deleteCardMutate]);

  // Effect to open modal and ensure state is synced
  // useEffect(() => {
  //   if (selectedCardId !== null && isModalOpen) {
  //     openModal(
  //       "Remove Card",
  //       <div className='text-sm'>
  //         Are you sure you want to remove this card?
  //       </div>,
  //       <Button
  //         onClick={handleDeleteCard}
  //         disabled={isDeletingCard}
  //         className={`text-xs ${isDeletingCard ? 'cursor-not-allowed opacity-50' : ''}`}
  //       >
  //         {isDeletingCard ? <ButtonLoader /> : "Yes"}
  //       </Button>,
  //       // handleDeleteCard
  //     );
  //   }
  // }, [selectedCardId, isModalOpen, isOpen, isDeletingCard]);

  const handleDeleteCardModal = (id) => {
    console.log("id", id)
    setSelectedCardId(id);
    openModal()
  };

  return (
    <>
      <div className="overflow-x-auto mt-6 no-scrollbar">
        <table className="table-auto w-full">
          <thead>
            <tr className='uppercase text-[12px] font-[400] py-4 whitespace-nowrap'>
              {['Card Holder', 'Card Number', 'Bank Name', 'Card Type', 'Card Expire On', 'Country Code', 'Action'].map(header => (
                <th key={header} className="px-2 py-1 text-left">{header}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {allTransactionData?.results?.map((item, index) => (
              <tr key={index} className="border-t text-[13px] whitespace-nowrap">
                <td className="px-2 py-4">{item.name}</td>
                <td className="px-2 py-4">{`**** **** **** ${item.last_4_digits}`}</td>
                <td className="px-2 py-4">{item.bank}</td>
                <td className="px-2 py-4 capitalize">{item.card_type}</td>
                <td className="px-2 py-4">{item.exp_month} / {item.exp_year}</td>
                <td className="px-2 py-4">{item.country_code}</td>
                <td className="px-2 py-4">
                  <Dropdown
                    overlayStyle={{ width: "250px" }}
                    menu={{
                      items: [
                        {
                          label: <div className='text-sm text-dashboard_sidebar_color font-light hover:bg-none bg-none'>Fund Card</div>,
                          key: '0',
                        },
                        {
                          label: <div className='text-sm text-dashboard_sidebar_color font-light hover:bg-none bg-none'>Freeze Card</div>,
                          key: '1',
                        },
                        {
                          label: <div className='text-sm text-dashboard_sidebar_color font-light hover:bg-none bg-none' onClick={() => handleDeleteCardModal(item.id)}>Remove Card</div>,
                          key: '2',
                        },
                      ],
                    }}
                    trigger={['click']}
                  >
                    <a onClick={(e) => e.preventDefault()}>
                      <div className='p-1 border border-auth_bg rounded-md w-8 h-8 flex items-center justify-center cursor-pointer'>
                        <HiOutlineDotsVertical />
                      </div>
                    </a>
                  </Dropdown>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {isOpenModal && <Deletecard />}
    </>
  );
};

export default VirtualCards;