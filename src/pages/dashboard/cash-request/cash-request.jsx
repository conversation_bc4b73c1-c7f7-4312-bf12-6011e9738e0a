"use client"

import React, { useState } from 'react'
import Button from '../../../components/button/button';
import { HiOutlinePlus } from "react-icons/hi";
import { useQuery } from '@tanstack/react-query';
import VirtualCards from './components/VirtualCards';
import Tabparams from '../../../components/Tabparams/Tabparams';
import { DashboardHeaderContent } from '../../../utils/Utils';
import Navigateback from '../../../components/navigateback/navigateback';
import { Link } from 'react-router-dom';
import { getAllCards } from '../../../../apis/card';

const Cashrequest = () => {


  const { handleSwitch, switchInfo } = Tabparams({ defaultTab: "virtual" });

  const { data: allCardsQuery, isPending: fetchingCards, refetch: refetchCards } = useQuery({
    queryKey: ["getallcards"],
    queryFn: getAllCards
  })

  return (

    <div>
      <Navigateback />

      <div className='flex items-center justify-between'>
        <DashboardHeaderContent header="Cash Request" subheader="Request for any amount of physical cash on Kompat" />
        <div className='flex items-center gap-4'>
        <Link to={"/dashboard/cash-request/register"} className='w-full'>
          <Button
          variant='secondary'
            className={"p-3 px-6 btn flex items-center justify-center gap-2 font-md text-xs whitespace-nowrap"}
          >Register as our agent</Button>
        </Link>

        <Link to={"/dashboard/cash-request/request"} className='w-full'>
          <Button
            className={"p-3 px-6 btn flex items-center justify-center gap-2 font-md text-xs whitespace-nowrap"}
          >Request for cash</Button>
        </Link>
        </div>
      </div>

      <div className="shadow-sm border rounded-lg border-border_color p-6">

        <div className='flex items-center gap-4 md:gap-x-6 border-b-1 border-0'>
          <div className={`flex items-center justify-center hover:text-auth_bg py-2 font-[500] text-[14px] cursor-pointer ${switchInfo === "virtual" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
            onClick={() => handleSwitch("virtual")}>
            <div>Pending Request ({allCardsQuery?.count})</div>
          </div>

          <div className={`flex items-center hover:text-auth_bg justify-center py-2 font-[500] text-[14px] cursor-pointer ${switchInfo === "physical" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
            onClick={() => handleSwitch("physical")}>
            <div>Successful Request</div>
          </div>
        </div>

        {switchInfo === "virtual" ? (
          allCardsQuery?.count <= 0 ? (
            <div className='text-sm mt-6'>You do not have any card with this profile</div>
          ) : (
            <VirtualCards refetchCards={refetchCards} allTransactionData={allCardsQuery} />

          )
        )
          :
          ""
        }

      </div>

    </div>
  )
}

export default Cashrequest;