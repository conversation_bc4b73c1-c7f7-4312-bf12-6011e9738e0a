import { deleteNotification, getAllNotification, markNotificationAsRead } from '../../../../apis/notification'
import Navigateback from '../../../components/navigateback/navigateback'
import { useCallModal } from '../../../hooks/useCallModal'
import { DashboardHeaderContent, formatDate } from '../../../utils/Utils'
import { useInfiniteQuery, useMutation } from '@tanstack/react-query';
import { useInView } from 'react-intersection-observer'; // Install this package
import React, { useEffect, useState } from 'react'
import toast from 'react-hot-toast'
import { Viewnotificcation } from './components/Viewnotificcation'
import { MdDelete } from "react-icons/md";
import { ButtonLoader } from '../../../utils/ButtonLoader'
import { MdNotifications, MdNotificationsActive } from "react-icons/md";




const Notification = () => {
  const { ref, inView } = useInView();

  const {
    data: allNotificationQuery,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch: notificationRefetch
  } = useInfiniteQuery({
    queryKey: ['notifications'],
    queryFn: ({ pageParam = 1 }) => getAllNotification(pageParam),
    getNextPageParam: (lastPage) => {
      if (lastPage?.next) {
        const nextUrl = new URL(lastPage.next);
        const nextPage = nextUrl.searchParams.get('page');
        return nextPage ? parseInt(nextPage) : undefined;
      }
      return undefined;
    },
    initialPageParam: 1,
    // Add these options to help with the initial load
    refetchOnMount: true,
    refetchOnWindowFocus: false
  });

  // Load more when scroll reaches bottom
  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage]);

  // Flatten all pages of results
  const notifications = allNotificationQuery?.pages?.flatMap(page => page.results) || [];
  console.log('notifications:', notifications);

  const { mutateAsync: markAsReadMuatate } = useMutation({ mutationFn: markNotificationAsRead })
  const { mutateAsync: deleteMutate, isPending: isDeleting } = useMutation({ mutationFn: deleteNotification })


  const handleDeleteNotification = async (id) => {
    const response = await deleteMutate({ id })
    toast.success(response?.detail)
    notificationRefetch()
  };


  const { closeModal, openModal, isOpenModal } = useCallModal();
  const [modalContent, setModalContent] = useState({ title: '', content: '' });

  const handleOpenModal = async (title, content, notificationId, isRead) => {
    setModalContent({ title, content });
    openModal();

    // Only call markAsRead if notification is unread
    if (!isRead) {
      try {
        await markAsReadMuatate({
          id: notificationId,
          read: true
        });
        notificationRefetch(); // Refresh the notifications list
      } catch (error) {
        toast.error('Failed to mark notification as read');
      }
    }
  };

  console.log('allNotificationQuery:', allNotificationQuery);

  return (
    <>
      <div>
        <Navigateback />
        <DashboardHeaderContent 
          header="Notification" 
          subheader="View, manage, and interact with various alerts, messages, and updates." 
        />
        <div className="shadow-sm border rounded-lg border-border_color">
          {notifications.length === 0 ? (
            <div className='flex items-center justify-center w-full py-10'>
              No available notification
            </div>
          ) : (
            <>
              {notifications.map((item) => (
                <div
                  onClick={() => handleOpenModal(item?.title, item?.content, item?.id, item?.read)}
                  key={item?.id}
                  className={`flex justify-between flex-col md:flex-row border-b py-6 px-6 cursor-pointer ${
                    item?.read ? 'bg-white' : 'bg-gray-100'
                  }`}
                >
                  <div className='flex items-center gap-x-6'>
                    {item?.read ? (
                      <MdNotificationsActive size={30} className='text-auth_bg' />
                    ) : (
                      <MdNotifications size={30} className='text-auth_bg' />
                    )}
                    <div>
                      <div className='text-xs font-[200] mb-2 w-full md:w-5/6'>
                        <div className='text-sm font-[600]'>{item?.title}</div>
                        <div dangerouslySetInnerHTML={{ __html: item?.content }}></div>
                      </div>
                      <div className='flex'>
                        <div className='flex items-center justify-center border border-auth_bg text-xs text-auth_bg hover:text-white bg-white hover:bg-auth_bg rounded-md p-2 px-8 cursor-pointer duration-500 transition-all'>View</div>
                      </div>
                    </div>
                  </div>
                  <div className='mt-4 md:mt-0 flex justify-between flex-col gap-2'>
                    <span className='text-xs text-general_gray_text font-[500] whitespace-nowrap'>{formatDate(item?.created_on)}</span>
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteNotification(item?.id)
                      }}
                      className=' relative .p-1'
                    >
                      <div className='bg-white shadow-lg hover:bg-gray-100 w-fit sm:w-full  rounded-md p-2 flex items-center text-gray-600 gap-2'>
                        {isDeleting ? (
                          <ButtonLoader />
                        ) : (
                          <React.Fragment>
                            <MdDelete size={20} className='text-red-500' /> <span className='text-[10px]'>Delete</span>
                          </React.Fragment>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {/* Loading trigger element */}
              <div
                ref={ref}
                className={`py-4 text-center ${!hasNextPage ? 'hidden' : ''}`}
              >
                {isFetchingNextPage ? (
                  <ButtonLoader />
                ) : hasNextPage ? (
                  'Load more...'
                ) : null}
              </div>
            </>
          )}
        </div>
      </div>

      {isOpenModal && (
        <Viewnotificcation 
          closeModal={closeModal} 
          title={modalContent.title} 
          content={modalContent.content} 
        />
      )}
    </>
  )
}

export default Notification;
