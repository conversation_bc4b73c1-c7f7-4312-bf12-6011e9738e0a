import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom';
import { billpaymentValidateAccountNumber, getBillersCategoryWithId, getChildrenBiller, getPackagesWithBillerId } from '../../../../../apis/billpayment';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Select } from 'antd';
import Navigateback from '../../../../components/navigateback/navigateback';
import { DashboardHeaderContent } from '../../../../utils/Utils';
import Input from '../../../../components/input/input';
import Button from '../../../../components/button/button';
import { PageLoader } from '../../../../utils/ButtonLoader';
import { HashLoader } from 'react-spinners';
import { FormatNumberWithCommas } from '../../../../utils/FormatNumberWithCommas';

const Categorybiller = () => {
  const navigate = useNavigate();
  const { state } = useLocation();
  const categorySelected = state && state.selectedCategory;

  const [selectBiller, setSelectBiller] = useState()
  const [selectChildBiller, setSelectChildBiller] = useState()
  const [selectPackage, setSelectPackage] = useState()

  const { data: billersQuery, isPending: isBillersFetching } = useQuery({
    queryKey: [categorySelected?.id, "getbillerwithid"],
    queryFn: () => getBillersCategoryWithId(categorySelected?.id),
    enabled: !!categorySelected?.id
  })

  const { data: childrenBillerQuery, isPending: isFetchingChildrenBiller } = useQuery({
    queryKey: [selectBiller?.id, "getchildrenbiller"],
    queryFn: () => getChildrenBiller(selectBiller?.id),
    enabled: !!selectBiller
  })

  // Update the packages query to use the child biller ID if available
  const { data: packagesQuery, isPending: isPackagesFetching } = useQuery({
    queryKey: [(selectChildBiller?.id || selectBiller?.id), "getpackageswithbillerid"],
    queryFn: () => getPackagesWithBillerId(selectChildBiller?.id || selectBiller?.id),
    enabled: !!(selectChildBiller?.id || selectBiller?.id)
  })

  const { mutateAsync: billpaymentsValidateAccNumberMutation, data: validationAccountNumberResponse, isPending: isAccountNumberValidating } = useMutation({
    mutationKey: ["billpaymentsValidateAccNumber"], mutationFn: billpaymentValidateAccountNumber
  })

  // Handler for selecting a biller
  const handleBiller = (billerId) => {
    const selected = billersQuery?.results?.find((biller) => biller.id === billerId);
    setSelectBiller(selected);
    // Reset child biller and package when parent biller changes
    setSelectChildBiller(null);
    setSelectPackage(null);
  };

  // Handler for selecting a child biller
  const handleChildBiller = (billerId) => {
    const selected = childrenBillerQuery?.results?.find((biller) => biller.id === billerId);
    setSelectChildBiller(selected);
    // Reset package when child biller changes
    setSelectPackage(null);
  };

  // console.log("selb", selectBiller)

  // Handler for selecting a package
  const handlePackageSelect = (packageId) => {
    const selected = packagesQuery?.results?.find((pkg) => pkg.id === packageId);
    setSelectPackage(selected);
  };

  const [remainingFormField, setRemainingFormField] = useState({
    amount: "",
    acc_number: ""
  })

  // Modified handler for amount field to handle comma-separated input
  const handleChangeFormField = (e) => {
    const { name, value } = e.target;

    if (name === "amount") {
      // Remove commas before processing
      const unformattedValue = value.replace(/,/g, '');

      // Check if it's a valid number
      if (unformattedValue === '' || !isNaN(unformattedValue)) {
        setRemainingFormField({ ...remainingFormField, [name]: unformattedValue });
      }
    } else {
      setRemainingFormField({ ...remainingFormField, [name]: value });
    }
  }

  const handleAccountNumberValidation = async () => {
    if (categorySelected?.config?.validate_account_number === true) {
      try {
        // Determine which biller ID to use (child or parent)
        const billerId = selectChildBiller?.id || selectBiller?.id;

        if (!billerId || !remainingFormField.acc_number) {
          return; // Don't validate if required fields are missing
        }

        const validationPayload = {
          biller: billerId,
          package: selectPackage?.id,
          account_number: remainingFormField.acc_number,
          meter_type: selectPackage?.name?.toLowerCase()
        };

        await billpaymentsValidateAccNumberMutation(validationPayload);
      } catch (error) {
        // console.error('Validation error', error);
        return;
      }
    }
  }

  useEffect(() => {
    // Only validate when we have all required fields
    const hasRequiredFields =
      (selectChildBiller?.id || selectBiller?.id) &&
      (selectPackage?.id || !packagesQuery?.count) &&
      remainingFormField.acc_number;

    if (hasRequiredFields) {
      handleAccountNumberValidation();
    }
  }, [selectBiller, selectChildBiller, selectPackage, remainingFormField.acc_number?.length === 11]);

  const handleProceed = async (e) => {
    e.preventDefault();

    // Determine which biller to use (child or parent)
    const billerToUse = selectChildBiller || selectBiller;

    // Determine the amount to use
    let amountToUse;
    if (selectPackage) {
      // If package is selected, use package price unless user can enter price
      amountToUse = selectPackage.user_enter_price ? remainingFormField.amount : selectPackage.price;
    } else {
      // If no package, use the entered amount
      amountToUse = remainingFormField.amount;
    }

    navigate(`/dashboard/billpayment/${categorySelected?.slug}/summary`, {
      state: {
        category: categorySelected,
        biller: billerToUse,
        parentBiller: selectChildBiller ? selectBiller : null, // Include parent biller if child is selected
        package: selectPackage,
        amount: amountToUse,
        accountNumber: remainingFormField.acc_number,
        accountName: validationAccountNumberResponse
      }
    });
  };

  // Check if form is valid for submission
  const isFormValid = () => {
    // Basic validation - we need a biller and account number
    if (!selectBiller || !remainingFormField.acc_number) {
      return false;
    }

    // If biller has children, we need a child biller selected
    if (selectBiller?.children_count > 0 && !selectChildBiller) {
      return false;
    }

    // If biller has packages, we need a package selected
    if (packagesQuery?.count > 0 && !selectPackage) {
      return false;
    }

    // If no packages and custom amount is needed, validate amount
    // if (selectBiller?.packages_count <= 0 && !remainingFormField.amount) {
    //   return false;
    // }

    return true;
  };

  return (
    <div>
      <Navigateback />
      <DashboardHeaderContent header={`${categorySelected?.name}`} subheader="Pay all your bills seamlessly. Buy airtime, data, electricity, Internet services, etc." />

      {isBillersFetching ? (
        <div className='text-xs mt-4'>
          <PageLoader />
        </div>
      ) : (

        <div className="shadow-sm border rounded-lg border-border_color p-6">

          <form onSubmit={handleProceed}>

            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-12 md:col-span-4">
                <span>{categorySelected?.config?.biller_label}</span>
              </div>
              <div className="col-span-12 md:col-span-8">

                <Select
                  size='large'
                  className='w-full md:w-[500px] placeholder:text-[12px]'
                  showSearch
                  placeholder={`Select ${categorySelected?.config?.biller_label}`}
                  optionFilterProp="label"
                  autoFocus
                  onChange={handleBiller}
                  onSearch={(value) => (value)}
                  options={
                    billersQuery ? billersQuery?.results?.map((item) => ({ key: item?.id, value: item?.id, label: <span className='capitalize'>{item?.name}</span> })) : []
                  }
                />
              </div>
            </div>

            <hr className='h-1 w-full text-border_color my-4' />

            {selectBiller?.children_count > 0 && (
              <React.Fragment>
                <div className="grid grid-cols-12 gap-6">
                  <div className="col-span-12 md:col-span-4">
                    <span>Sub-{categorySelected?.config?.biller_label}</span>
                  </div>
                  <div className="col-span-12 md:col-span-8">
                    {isFetchingChildrenBiller ? (
                      <div className="flex items-center">
                        <HashLoader size={15} />
                        <span className="ml-2 text-sm">Loading sub-{categorySelected?.config?.biller_label}...</span>
                      </div>
                    ) : (
                      <Select
                        size='large'
                        className='w-full md:w-[500px] placeholder:text-[12px]'
                        showSearch
                        placeholder={`Select Sub-${categorySelected?.config?.biller_label}`}
                        optionFilterProp="label"
                        autoFocus
                        onChange={handleChildBiller}
                        onSearch={(value) => (value)}
                        options={
                          childrenBillerQuery ? childrenBillerQuery?.results?.map((item) => ({ key: item?.id, value: item?.id, label: <span className='capitalize'>{item?.name}</span> })) : []
                        }
                      />
                    )}
                  </div>
                </div>
                <hr className='h-1 w-full text-border_color my-4' />
              </React.Fragment>
            )}

            {packagesQuery?.count > 0 && (
              <React.Fragment>
                <div className="grid grid-cols-12 gap-6">
                  <div className="col-span-12 md:col-span-4">
                    <span>Packages</span>
                  </div>
                  <div className="col-span-12 md:col-span-8">
                    {isPackagesFetching ? (
                      <div className="flex items-center">
                        <HashLoader size={15} />
                        <span className="ml-2 text-sm">Loading packages...</span>
                      </div>
                    ) : (
                      <Select
                        size='large'
                        className='w-full md:w-[500px] placeholder:text-[12px]'
                        showSearch
                        placeholder={`Select Package`}
                        optionFilterProp="label"
                        autoFocus
                        onChange={handlePackageSelect}
                        onSearch={(value) => (value)}
                        options={
                          packagesQuery ? packagesQuery?.results?.map((item) => ({
                            key: item?.id, value: item?.id, label: `${item?.name} ${item?.price ? `- ${item?.currency?.currency_symbol} ${parseFloat(item?.price).toLocaleString()}` : ''}`
                          })) : []
                        }
                      />
                    )}
                  </div>
                </div>
                <hr className='h-1 w-full text-border_color my-4' />
              </React.Fragment>
            )}

            {/* Show amount input if no packages or if package allows custom price */}
            {(
              // For parent biller with no children selected
              (selectBiller && !selectChildBiller && selectBiller.packages_count <= 0) ||
              // For child biller when selected
              (selectChildBiller && selectChildBiller.packages_count <= 0) ||
              // For package with user_enter_price
              (selectPackage && selectPackage.user_enter_price === true)
            ) && (
                <React.Fragment>
                  <div className="grid grid-cols-12 gap-6">
                    <div className="col-span-12 md:col-span-4">
                      <span>Enter Amount</span>
                    </div>
                    <div className="col-span-12 md:col-span-8">
                      <div className='w-full md:w-[500px]'>
                        <Input
                          onChange={handleChangeFormField}
                          value={FormatNumberWithCommas(remainingFormField.amount)}
                          name={"amount"}
                          type='text'
                          placeholder={"Enter Amount"}
                          className={"p-3"}
                          inputMode="numeric"
                        />
                      </div>
                    </div>
                  </div>
                  <hr className='h-1 w-full text-border_color my-4' />
                </React.Fragment>
              )}

            {/* Show fixed amount for selected package */}
            {selectPackage && !selectPackage?.user_enter_price && (
              <React.Fragment>
                <div className="grid grid-cols-12 gap-6">
                  <div className="col-span-12 md:col-span-4">
                    <span>Amount</span>
                  </div>
                  <div className="col-span-12 md:col-span-8">
                    <div className='w-full md:w-[500px]'>
                      <Input
                        disabled={true}
                        value={`${selectPackage?.currency?.currency_symbol} ${parseFloat(selectPackage?.price).toLocaleString()}`}
                        name={"amount"}
                        type='text'
                        className={"p-3"}
                      />
                    </div>
                  </div>
                </div>
                <hr className='h-1 w-full text-border_color my-4' />
              </React.Fragment>
            )}

            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-12 md:col-span-4">
                <span>{categorySelected?.config?.account_number_label}</span>
              </div>
              <div className="col-span-12 md:col-span-8">
                <div className='w-full md:w-[500px]'>
                  <Input
                    onChange={handleChangeFormField}
                    value={remainingFormField.acc_number}
                    name={"acc_number"}
                    type='number'
                    pattern="\d{0,11}"
                    onInput={(e) => { e.target.value = e.target.value.slice(0, 11); }}
                    placeholder={`Enter ${categorySelected?.config?.account_number_label}`}
                    className={"p-3"}
                  />
                </div>
              </div>
            </div>

            <hr className='h-1 w-full text-border_color my-4' />

            {validationAccountNumberResponse && (
              <div className="grid grid-cols-12 gap-6 my-4">
                <div className="col-span-12 md:col-span-4">
                  <span>Account Name</span>
                </div>
                <div className="col-span-12 md:col-span-8">
                  <div className='w-full md:w-[500px]'>
                    <Input type='text' disabled={true} value={validationAccountNumberResponse?.detail} className={"p-3"} />
                  </div>
                </div>
              </div>
            )}

            {isAccountNumberValidating && (
              <div className="fixed inset-0 w-full h-full bg-black bg-opacity-50 flex justify-center items-center z-[10] transition-all duration-1500 ease-in-out">
                <div className="flex items-center justify-center gap-4">
                  <HashLoader size={20} />
                </div>
              </div>
            )}

            <div className='flex items-center justify-center mt-6'>
              <div>
                <Button
                  type='submit'
                  className={"p-3 px-16 text-xs"}
                  disabled={!isFormValid()}
                >
                  Proceed
                </Button>
              </div>
            </div>

          </form>
        </div>
      )}

    </div>
  )
}

export default Categorybiller;
