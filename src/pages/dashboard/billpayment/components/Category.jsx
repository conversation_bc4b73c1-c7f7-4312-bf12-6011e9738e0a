import { Link } from "react-router-dom"

export const Category = ({ category, onClickCategory }) => {
  // console.log("cat", category)
  return (
    <>
      <div
        // key={item?.id}
        onClick={() => onClickCategory(category)}
        className="cursor-pointer hover:bg-gray-100 transition-all duration-500 ease-in-out flex items-center gap-2 sm:gap-4 border rounded-md p-1 sm:p-3 sm:px-4 truncate"
      >
        <div className="p-2 sm:p-0">
          {!category?.icon ? (
            <img src={"/images/logo/faviconblue.png"} className='w-8 h-8 sm:w-10 sm:h-10 rounded-full' alt="img" />
          ) : (
            <img src={category?.icon} className='w-8 h-8 sm:w-10 sm:h-10 rounded-full' alt="img" />
          )}
        </div>
        <div className="flex flex-col justify-center gap-1 truncate">
          <div className='font-medium text-xs sm:text-sm capitalize truncate'>{category?.name}</div>
          {category?.description && (
            <p className='text-xs truncate font-thin'>{category?.description}</p>
          )}
        </div>
      </div>
    </>
  )
}