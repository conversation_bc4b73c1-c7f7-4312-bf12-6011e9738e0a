"use client"

import React, { useState, useEffect } from 'react'
import { allCategory } from '../../../../apis/billpayment';
import { useQuery } from '@tanstack/react-query';
import { Select } from 'antd';
import { Category } from './components/Category';
import { PageLoader } from '../../../utils/ButtonLoader';
import Navigateback from '../../../components/navigateback/navigateback';
import { useNavigate } from 'react-router-dom';
import { DashboardHeaderContent } from '../../../utils/Utils';
import { listOfCountries } from '../../../../apis/location';

const Billpayment = () => {
  const navigate = useNavigate()

  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedCountry, setSelectedCountry] = useState("");

  // Get default country from localStorage
  useEffect(() => {
    const defaultCountry = localStorage.getItem("Kompat_userData")
    if (defaultCountry) {
      const defaultCountryObj = JSON.parse(defaultCountry)
      const defaultCountryCode = defaultCountryObj?.userData?.location?.country?.alpha2code
      setSelectedCountry(defaultCountryCode)
    }
  }, [])

  const handleSelectedConfig = (category) => {
    setSelectedCategory(category)
    navigate(`/dashboard/billpayment/${category.slug}`, { state: { selectedCategory: category } })
  }

  const handleCountryChange = (value) => {
    setSelectedCountry(value);
  };

  const { data: countryData, isPending: loadingCountries } = useQuery({
    queryKey: ["getallcountries"],
    queryFn: listOfCountries,
  });

  const { data: allCategoryQuery, isPending: fetchingAllCategory, refetch } = useQuery({
    queryKey: ["getallbillpaymentcategory", selectedCountry],
    queryFn: () => allCategory({ country_alpha2code: selectedCountry }),
    enabled: !!selectedCountry,
  });

  console.log("sel", selectedCountry)

  // Refetch categories when country changes
  useEffect(() => {
    if (selectedCountry) {
      refetch();
    }
  }, [selectedCountry, refetch]);

  return (
    <>
      <Navigateback />
      <DashboardHeaderContent header="Bill Payment" subheader="Pay all your bills seamlessly. Buy airtime, data, electricity, Internet services, etc." />
      <div className="shadow-sm border rounded-lg border-border_color p-6">

        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 md:col-span-4">
            <span>Country</span>
          </div>
          <div className="col-span-12 md:col-span-8">
            <Select
              size='large'
              showSearch
              optionFilterProp="label"
              placeholder="Select Country"
              loading={loadingCountries}
              value={selectedCountry}
              onChange={handleCountryChange}
              options={countryData?.results?.map((country) => ({
                label: country.name,
                value: country.alpha2code
              })) || []}
              className="w-full md:w-[500px] placeholder:text-[12px]"
            />
          </div>
        </div>

        <hr className='h-1 w-full text-border_color my-4' />

        {fetchingAllCategory && (
            <PageLoader />
        )}

        <div className="grid md:grid-cols-4 grid-cols-1 gap-6">
          {!fetchingAllCategory && allCategoryQuery?.results?.length === 0 && (
            <div className="col-span-full text-center py-4">
              No bill payment categories available for this {selectedCountry}
            </div>
          )}
          {allCategoryQuery?.results?.map((category) => (
            <Category key={category?.id} category={category} onClickCategory={handleSelectedConfig} />
          ))}
        </div>

      </div>
    </>
  )
}

export default Billpayment;