import { useLocation, useNavigate } from "react-router-dom";
import { useCallModal } from "../../../../hooks/useCallModal";
import Navigateback from "../../../../components/navigateback/navigateback";
import { DashboardHeaderContent } from "../../../../utils/Utils";
import Button from "../../../../components/button/button";
import { IoIosArrowRoundBack } from "react-icons/io";
import { TfiControlForward } from "react-icons/tfi";
import { PaymentMethod } from "../../../../components/payment/PaymentMethod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { getWallet } from "../../../../../apis/wallet";
import { billpaymentPayment } from "../../../../../apis/billpayment";
import toast from "react-hot-toast";
import useServiceStore from "../../../../store/useServiceStore";
import { useEffect } from "react";


export const Billpaymentsummary = () => {
  const { state } = useLocation();

  console.log("allstates", state)

  const navigate = useNavigate()

  const handleNavigateback = (e) => {
    e.preventDefault()
    navigate(-1);
  }

  const { openModal, isOpenModal, closeModal } = useCallModal();

  const { mutateAsync, isPending: isSendingMoneyInternalPending } = useMutation({
    mutationKey: ["billpayment-payment"],
    mutationFn: billpaymentPayment,
  });

  const { selectedService } = useServiceStore();

  const handleBillPaymentPay = async (transactionPin) => {

    const serviceSlug = "bill payment";
    

    const apiPayload = {
      biller: state?.biller?.id,
      account_number: state?.accountNumber,
      amount: state?.amount,
      payment_method: "wallet",
      save_beneficiary: false,
      category: state?.category?.id,
      data: {},
      package: state?.package?.id,
      quantity: 1,
      transaction_pin: transactionPin,
      service_id: selectedService?.id || serviceSlug,
      // callback_url: "",
      // card: 0
    }
    try {
      const response = await mutateAsync(apiPayload)
      console.log("myresponse", response);
      toast.success(response?.detail || "Bill Payment successful");
      navigate(`/dashboard/transactions/${response?.data?.transaction_type}/${response?.data?.id}/`)
    } catch (error) {
      console.log("error", error);
      toast.error(error.detail || "An error occurred");
    }
  }

  // console.log("response", {formData, validatePayload, validateData})

  const { formData, validatePayload, validateData } = state
  console.log("continue", formData)

  const { data: getWalletQuery } = useQuery({ queryKey: ["getwallet"], queryFn: getWallet })

  const getDefaultWallet = getWalletQuery?.results?.filter((res) => res?.default === true)
  console.log("default", getDefaultWallet)
  const iterateDefaultWallet = getDefaultWallet?.[0]

  return (

    <div className="">
      <Navigateback />
      <DashboardHeaderContent header="Bill Payment Summary" subheader="Review the transaction you are about to do." />

      <div className='.w-full .md:w-[70%] space-y-10 md:mt-10 capitalize'>
        <div className="grid grid-cols-5 gap-y-2 text-sm">
          <div className="col-span-5 md:col-span-2">
            <span>Category:</span>
          </div>
          <div className="col-span-5 md:col-span-3 text-lightblack">
            <div className='w-full'>
              <span>{state.category?.name}</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-5 gap-y-2 text-sm">
          <div className="col-span-5 md:col-span-2">
            <span>Biller:</span>
          </div>
          <div className="col-span-5 md:col-span-3 text-lightblack">
            <div className='w-full'>
              <span>{state?.biller?.name}</span>
            </div>
          </div>
        </div>
        {state?.package && (
          <div className="grid grid-cols-5 gap-y-2 text-sm">
            <div className="col-span-5 md:col-span-2">
              <span>Package:</span>
            </div>
            <div className="col-span-5 md:col-span-3 text-lightblack">
              <div className='w-full'>
                <span>{state?.package?.name}</span>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-5 gap-y-2 text-sm">
          <div className="col-span-5 md:col-span-2">
            <span> Biller No:</span>
          </div>
          <div className="col-span-5 md:col-span-3 text-lightblack">
            <div className='w-full'>
              <span>{state.accountNumber}</span>
            </div>
          </div>
        </div>
        {state?.accountName && (
          <div className="grid grid-cols-5 gap-y-2 text-sm">
            <div className="col-span-5 md:col-span-2">
              <span> Account Name:</span>
            </div>
            <div className="col-span-5 md:col-span-3 text-lightblack">
              <div className='w-full'>
                <span>{state.accountName.detail}</span>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-5 gap-y-2 text-sm">
          <div className="col-span-5 md:col-span-2">
            <span> Amount:</span>
          </div>
          <div className="col-span-5 md:col-span-3 text-lightblack">
            <div className='w-full'>
              <span>{parseFloat(state.amount).toLocaleString()}</span>
            </div>
          </div>
        </div>


        <div className="flex items-center gap-4">
          <div>
            <Button
              onClick={handleNavigateback}
              variant='transparent'
              className={"p-3 px-6 flex items-center justify-center gap-2 text-xs"}
            >
              <IoIosArrowRoundBack className='text-lg' />
              <span>Back</span>
            </Button>
          </div>

          <div>
            <Button
              type='Submit'
              onClick={() => openModal()}
              className={"p-3 px-6 btn flex items-center justify-center gap-2 font-md text-xs"}
            >
              <span>Proceed</span>
              <TfiControlForward className='text-lg' />
            </Button>
          </div>

        </div>

      </div>

      {isOpenModal && (<PaymentMethod walletBal={iterateDefaultWallet} closeModal={closeModal} continuation={handleBillPaymentPay} />)}

    </div>
  )
}