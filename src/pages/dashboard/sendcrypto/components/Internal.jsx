import React, { useCallback, useEffect, useState } from 'react';
import { CiSearch } from 'react-icons/ci';
import Button from '../../../../components/button/button';
import { Select } from 'antd';
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query';
import { currencycodeApi, getAllCurrencies } from '../../../../../apis/currency';
import { bankAccount } from '../../../../../apis/account';
import { validateAccountNumber } from '../../../../../apis/settings';
import Input from '../../../../components/input/input';
import { PageLoader } from '../../../../utils/ButtonLoader';
import { HashLoader } from 'react-spinners';
import { useNavigate } from 'react-router-dom';
import { FormatNumberWithCommas } from '../../../../utils/FormatNumberWithCommas';
import useCurrencySearch from '../../../../hooks/useCurrencySearch';

const Internal = () => {
  const navigate = useNavigate();

  // Get all currency
  const additionalParams = {
    is_crypto: true,
  };

  const {
    searchTerm,
    setSearchTerm,
    setIsSearching,
    isSearching,
    allCurrencyInfiniteData,
    isCurrencyFetching,
    isFetchingNextCurrencyPage,
    debouncedSearch,
    handleScroll,
  } = useCurrencySearch('', additionalParams);

  // Get all beneficiaries
  const { data: internalBeneficiary, isPending: loadingBeneficiary } = useQuery({
  queryKey: ['getallbeneficiary'],
    queryFn: () => bankAccount({ is_internal_beneficiary: true }),
  });

  const [searchQuery, setSearchQuery] = useState('');

  // Function to extract initials
  const getInitials = (name) => {
    const names = name.split(' ');
    const firstNameInitial = names[0]?.charAt(0).toUpperCase() || '';
    const lastNameInitial = names[names.length - 1]?.charAt(0).toUpperCase() || '';
    return `${firstNameInitial}${lastNameInitial}`;
  };

  // Filter beneficiaries based on search query
  const filteredBeneficiaries = internalBeneficiary?.results?.filter((item) =>
    item?.account_name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const [validatePayload, setValidatePayload] = useState({
    internal: true,
    account_number: '',
  });

  const {
    mutateAsync,
    data: validateData,
    isPending: isValidating,
    isError: validateError,
  } = useMutation({ mutationFn: validateAccountNumber });

  useEffect(() => {
    const handleValidateAccountNumber = () => {
      mutateAsync({ ...validatePayload });
    };

    if (validatePayload.account_number.length === 10) {
      handleValidateAccountNumber();
    }
  }, [validatePayload, mutateAsync]);

  const [formData, setFormData] = useState({
    currency: '',
    amount: '',
    description: '',
    accountNo: '',
    accountName: '',
    currencySymbol: '',
  });

  const handleCurrencyChange = (value) => {
    const allCurrencies = allCurrencyInfiniteData?.pages.flatMap((page) => page.results) || [];
    const selectedCurrency = allCurrencies.find((item) => item?.code === value);
    setFormData((prev) => ({
      ...prev,
      currency: value,
      currencySymbol: selectedCurrency || '',
    }));
  };

  const handleAmountChange = (e) => {
    const inputValue = e.target.value;
    
    // Handle empty input or backspace
    if (inputValue === '') {
      setFormData("");
      return;
    }
    
    const numericValue = inputValue.replace(/,/g, '');
    if (!isNaN(numericValue)) {
      setFormData(numericValue);
    }
  };

  const handleBeneficiaryClick = (accountNumber) => {
    setValidatePayload((prev) => ({
      ...prev,
      account_number: accountNumber,
    }));
  };

  const check =
    !validatePayload?.account_number ||
    !validateData?.account_name ||
    !formData?.currency ||
    !formData?.amount ||
    formData?.amount > formData?.currencySymbol?.balance;

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!check) {
      navigate('/dashboard/sendmoney/transaction-summary', {
        state: { formData, validatePayload, validateData },
      });
    }
  };

  const { data: networklist, isPending: isLoadingNetwork } = useQuery({
    queryKey: ["getallcryptonetworks", formData?.currency],
    queryFn: () => currencycodeApi(formData?.currency),
  });

  return (
    <>
      {loadingBeneficiary ? (
        <div className="flex w-full items-center justify-center text-auth_bg">
          <PageLoader />
        </div>
      ) : (
        <form onSubmit={handleSubmit}>
          <div className="md:leading-6 my-6">
            <div className="font-[500] text-[18px]">Send cryptocurrency internally</div>
            <div className="font-[300] text-[13px]">
              Fill in the required information to send cryptocurrency to a kompat account.
            </div>
          </div>

          <hr className="h-1 w-full text-border_color my-4" />

          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-12 md:col-span-4">
              <span>Currency</span>
            </div>
            <div className="col-span-12 md:col-span-8">
              <Select
                size="large"
                className="w-full md:w-[500px] placeholder:text-[12px]"
                showSearch
                placeholder="Select Currency"
                optionFilterProp="label"
                autoFocus
                onChange={handleCurrencyChange}
                onSearch={(value) => {
                  setIsSearching(true);
                  debouncedSearch(value);
                }}
                filterOption={false}
                options={
                  allCurrencyInfiniteData?.pages.flatMap((page) =>
                    page.results.map((item) => ({
                      key: item.id,
                      value: item.code,
                      label: `${item.name} (${item.code})`,
                    }))
                  ) || []
                }
                onPopupScroll={handleScroll}
                notFoundContent={
                  isCurrencyFetching || isSearching ? (
                    <div className="flex justify-center p-2">
                      <PageLoader size={20} color="#2A4365" />
                    </div>
                  ) : null
                }
                dropdownRender={(menu) => (
                  <>
                    {menu}
                    {isFetchingNextCurrencyPage && (
                      <div className="flex justify-center p-2">
                        <PageLoader size={20} color="#2A4365" />
                      </div>
                    )}
                  </>
                )}
              />
              {formData?.currency && (
                <div className="text-[12px] mt-2">
                  Balance: {`${formData?.currencySymbol?.symbol}${parseFloat(
                    formData?.currencySymbol?.balance
                  ).toLocaleString()}`}
                </div>
              )}
            </div>
          </div>

          <hr className="h-1 w-full text-border_color my-4" />

          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-12 md:col-span-4">
              <span>Select Network</span>
            </div>
            <div className="col-span-12 md:col-span-8">
              <Select
                size='large'
                showSearch
                optionFilterProp="label"
                placeholder="Select Network"
                loading={isLoadingNetwork}
                disabled={isLoadingNetwork}
                options={networklist?.crypto_network_list?.map((network) => ({
                  label: <div className='capitalize'>{network.network_name}</div>,
                  value: network.network
                })) || []}
                className="w-full md:w-[500px] placeholder:text-[12px]"
              />

            </div>
          </div>

          {internalBeneficiary?.count > 0 && (
            <hr className="h-1 w-full text-border_color my-4" />
          )}

          {internalBeneficiary?.count > 0 && (
            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-12 md:col-span-4">
                <span>Saved Beneficiaries</span>
              </div>

              <div className="col-span-12 md:col-span-8">
                <div className="w-full md:w-[500px] space-y-4">
                  <div className="p-2 border rounded-[8px] text-[#404850] text-[14px] flex items-center gap-2">
                    <CiSearch />
                    <input
                      type="search"
                      placeholder="Search Beneficiaries"
                      className="w-full border-none outline-none focus:ring-0"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  <div className="p-2 border rounded-[8px] text-[#404850] text-[14px] flex items-center gap-2 overflow-x-scroll no-scrollbar">
                    {filteredBeneficiaries?.map((item) => (
                      <div
                        key={item?.id}
                        className="p-2 hover:bg-gray-100 transition-all duration-500 ease-in-out rounded-md flex items-center justify-center flex-col gap-2 cursor-pointer"
                        onClick={() => handleBeneficiaryClick(item.account_number)}
                      >
                        <div className="w-10 h-10 rounded-full bg-[#EBECF5] flex items-center justify-center">
                          {getInitials(item?.account_name)}
                        </div>
                        <div className="text-[8px] whitespace-nowrap">
                          {item?.account_name.slice(0, 15)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          <hr className="h-1 w-full text-border_color my-4" />

          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-12 md:col-span-4">
              <span>Kompat Tag</span>
            </div>
            <div className="col-span-12 md:col-span-8">
              <div className="w-full md:w-[500px]">
                <Input
                  onInput={(e) => {
                    e.target.value = e.target.value.slice(0, 10);
                  }}
                  value={validatePayload?.account_number}
                  onChange={(e) =>
                    setValidatePayload((prev) => ({
                      ...prev,
                      account_number: e.target.value,
                    }))
                  }
                  type="number"
                  placeholder="Enter Kompat Tag (Account Number)"
                  className="p-3"
                />
                {isValidating && (
                  <div className="fixed inset-0 w-full h-full bg-black bg-opacity-50 flex justify-center items-center z-[10] transition-all duration-1500 ease-in-out">
                    <div className="flex items-center justify-center gap-4">
                      <HashLoader size={20} />
                    </div>
                  </div>
                )}

                {validateError && (
                  <span className="flex justify-start my-2 mt-1 font-[400] text-sm text-red-500">
                    Account name could not be fetched
                  </span>
                )}
              </div>
            </div>
          </div>

          {validateData && (
            <hr className="h-1 w-full text-border_color my-4" />
          )}

          {validateData && (
            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-12 md:col-span-4">
                <span>Account Name</span>
              </div>
              <div className="col-span-12 md:col-span-8">
                <div className="w-full md:w-[500px]">
                  <Input
                    type="text"
                    disabled={true}
                    value={validateData?.account_name}
                    className="p-3"
                  />
                </div>
              </div>
            </div>
          )}

          <hr className="h-1 w-full text-border_color my-4" />

          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-12 md:col-span-4">
              <span>Amount</span>
            </div>
            <div className="col-span-12 md:col-span-8">
              <div className="w-full md:w-[500px]">
                <Input
                  type="text"
                  value={FormatNumberWithCommas(formData?.amount)}
                  onChange={handleAmountChange}
                  placeholder="Enter Amount"
                  className="p-3"
                  inputMode="numeric" // Add inputMode for numeric keyboard
                  min="0"
                />
              </div>
            </div>
          </div>

          <hr className="h-1 w-full text-border_color my-4" />

          <div className="flex items-center justify-center">
            <div>
              <Button
                type="submit"
                disabled={check}
                className={`p-3 px-16 text-xs ${check && ` btn_opacity`}`}
              >
                Continue
              </Button>
            </div>
          </div>
        </form>
      )}
    </>
  );
};

export default Internal;