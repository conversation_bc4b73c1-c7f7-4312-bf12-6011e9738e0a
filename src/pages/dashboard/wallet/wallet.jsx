import React, { useState } from 'react';
import { HiOutlinePlus } from "react-icons/hi";
import { useQuery } from '@tanstack/react-query';
import { PageLoader } from '../../../utils/ButtonLoader';
import Tabparams from '../../../components/Tabparams/Tabparams';
import Button from '../../../components/button/button';
import { DashboardHeaderContent } from '../../../utils/Utils';
import { getWallet } from '../../../../apis/wallet';
import Navigateback from '../../../components/navigateback/navigateback';
import Addwallet from './components/Addwallet';
import { useCallModal } from '../../../hooks/useCallModal';
import GenericTable from '../../../components/table/GenericTable';
import { Dropdown } from 'antd';
import { Link, useNavigate } from 'react-router-dom';
import { IoMdEye } from "react-icons/io";
import EnterAmount from './components/fundwallet/withcard/EnterAmount';
import { BankInfo } from './components/fundwallet/withbanktransfer/BankInfo';
import SelectCryptoNetwork from './components/fundwallet/withcrypo/SelectCryptoNetwork';

const Wallet = () => {
  const { handleSwitch, switchInfo } = Tabparams({ defaultTab: "allwallet" });
  const navigate = useNavigate();
  const [selectedWallet, setSelectedWallet] = useState(null);

  // Get all wallets with appropriate filter based on tab
  const { data: getWalletQuery, isPending: isWalletFetching } = useQuery({
    queryKey: ["getwallet", switchInfo], 
    queryFn: () => getWallet({ 
      is_crypto: switchInfo === "cryptowallet" ? true : 
                 switchInfo === "fiatwallet" ? false : 
                 "" 
    })
  });

  const { isOpenModal, openModal, closeModal } = useCallModal();
  const { isOpenModal: isOpenModalBT, openModal: openModalBT, closeModal: closeModalBT } = useCallModal();
  const { isOpenModal: isOpenModalCT, openModal: openModalCT, closeModal: closeModalCT } = useCallModal();

  const handleOpenModal = (wallet, type) => {
    setSelectedWallet(wallet);

    if (type === 'card') {
      if (isOpenModalBT) {
        closeModalBT();
      }
      openModal();
    } else if (type === 'bank') {
      if (isOpenModal) {
        closeModal();
      }
      openModalBT();
    } else if (type === 'crypto') {
      if (isOpenModal) {
        closeModal();
      }
      openModalCT();
    }
  };

  // Add a new function to handle opening the Add Wallet modal
  const handleAddWalletModal = () => {
    setSelectedWallet(null); // Reset selected wallet to ensure we show the Add Wallet modal
    openModal();
  };

  // Define columns for the wallet table
  const columns = [
    {
      key: 'currency',
      header: 'Currency',
      render: (item) => (
        <div className='flex items-center gap-2'>
          <img 
            src={!item.currency.image ? '/images/logo/faviconblue.png' : item.currency.image} 
            className='rounded-full w-4 h-4 sm:w-4 sm:h-4' 
            alt='img' 
          />
          <span>
            {item.currency.code} 
            {item.currency?.is_crypto && (
              <span className='border p-1 px-2 text-[10px] capitalize rounded-md ml-1'>
                {item?.network?.network_name}
              </span>
            )}
          </span>
        </div>
      )
    },
    {
      key: 'balance',
      header: 'Balance',
      render: (item) => (
        <span className="whitespace-nowrap">
          {item?.currency?.symbol} {parseFloat(item.balance).toLocaleString()}
        </span>
      )
    },
    {
      key: 'fund',
      sortable: false,
      render: (item) => (
        <Dropdown
          menu={{
            items: getDropdownItems(item),
          }}
          trigger={['click']}
          arrow={false}
        >
          <a onClick={(e) => e.preventDefault()}>
            <div className="whitespace-nowrap flex items-center text-auth_bg gap-2 font-[500] hover:bg-gray-100 text-[10px] py-2 px-4 w-fit rounded-md">
              <span><HiOutlinePlus /></span>
              <span className='cursor-pointer'>Fund Wallet</span>
            </div>
          </a>
        </Dropdown>
      )
    },
    {
      key: 'transactions',
      sortable: false,
      render: (item) => (
        <div 
          className="whitespace-nowrap flex items-center text-auth_bg gap-2 font-[500] hover:bg-gray-100 text-[10px] py-2 px-4 w-fit rounded-md cursor-pointer" 
          onClick={() => navigate(`/dashboard/transactions/${item?.currency?.code}`)}
        >
          <span><IoMdEye size={14} /></span>
          <span>View Transactions</span>
        </div>
      )
    }
  ];

  const getDropdownItems = (wallet) => {
    const items = [
      {
        label: (
          <div onClick={() => handleOpenModal(wallet, 'card')} className='text-xs font-md py-1'>
            Fund with Card
          </div>
        ),
        key: '0',
      },
      {
        label: (
          <div onClick={() => handleOpenModal(wallet, 'bank')} className='text-xs font-md py-1'>
            Fund With Bank Transfer
          </div>
        ),
        key: '1',
      },
      {
        label: (
          <Link to={`/dashboard/currencyexchange?swapTo=${wallet.currency.code}`} className='text-xs font-md py-1'>
            Swap into this wallet
          </Link>
        ),
        key: '2',
      },
    ];

    // Add "Fund With Crypto Transfer" only if is_crypto is true
    if (wallet?.currency?.is_crypto) {
      items.push({
        label: (
          <div onClick={() => handleOpenModal(wallet, 'crypto')} className='text-xs font-md py-1'>
            Fund With Crypto Transfer
          </div>
        ),
        key: '3',
      });
    }

    return items;
  };

  // Get appropriate empty message based on wallet type
  const getEmptyMessage = () => {
    switch (switchInfo) {
      case 'fiatwallet':
        return "No available fiat wallet found";
      case 'cryptowallet':
        return "No available crypto wallet found";
      default:
        return "No available wallet found";
    }
  };

  return (
    <>
      <div>
        <Navigateback />
        <div className='flex items-center justify-between'>
          <DashboardHeaderContent 
            header="Wallets" 
            subheader="This overview provides a comprehensive snapshot of general information over time." 
          />
          <div>
            <Button
              onClick={handleAddWalletModal} // Use the new function instead of directly calling openModal
              className={"p-3 btn flex items-center justify-center gap-2 font-md text-xs whitespace-nowrap"}
            >
              <HiOutlinePlus size={16} className='text-white' /> Add New Wallet
            </Button>
          </div>
        </div>

        <div className="shadow-sm border rounded-lg border-border_color .p-6">
          <div className='px-6 pt-6 flex items-center gap-x-4 md:gap-x-6 border-b-1 border-0 overflow-x-auto no-scrollbar'>
            <div 
              className={`flex items-center justify-center hover:text-auth_bg py-2 font-[500] text-[10px] md:text-[14px] whitespace-nowrap cursor-pointer ${switchInfo === "allwallet" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
              onClick={() => handleSwitch("allwallet")}
            >
              <div>All Wallets</div>
            </div>

            <div 
              className={`flex items-center justify-center hover:text-auth_bg py-2 font-[500] text-[10px] md:text-[14px] whitespace-nowrap cursor-pointer ${switchInfo === "fiatwallet" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
              onClick={() => handleSwitch("fiatwallet")}
            >
              <div>Fiat Wallets</div>
            </div>

            <div 
              className={`flex items-center justify-center hover:text-auth_bg py-2 font-[500] text-[10px] md:text-[14px] whitespace-nowrap cursor-pointer ${switchInfo === "cryptowallet" ? 'border-b-2 border-auth_bg text-auth_bg' : ''}`}
              onClick={() => handleSwitch("cryptowallet")}
            >
              <div>Crypto-currency Wallets</div>
            </div>
          </div>

          {getWalletQuery?.count <= 0 ? (
            <div className='text-sm mt-6'>
              You don't have any {switchInfo === "cryptowallet" ? "crypto" : switchInfo === "fiatwallet" ? "fiat" : ""} wallet on your account yet
            </div>
          ) : (
            <GenericTable
              columns={columns}
              data={getWalletQuery}
              isPending={isWalletFetching}
              emptyMessage={getEmptyMessage()}
              itemsPerPage={10}
            />
          )}
        </div>
      </div>

      {(isOpenModal && !selectedWallet) && <Addwallet closeModal={closeModal} />}
      {isOpenModal && selectedWallet && <EnterAmount closeModal={closeModal} wallet={selectedWallet} />}
      {isOpenModalBT && <BankInfo closeModal={closeModalBT} wallet={selectedWallet} />}
      {isOpenModalCT && <SelectCryptoNetwork closeModal={closeModalCT} wallet={selectedWallet} />}
    </>
  );
};
 
export default Wallet;
