import React from 'react'
import Modal from '../../../../../../components/modal/Modal'
import { DashboardHeaderContent, OnboardingLogo } from '../../../../../../utils/Utils'
import { useQuery } from '@tanstack/react-query'
import Button from '../../../../../../components/button/button'
import { ButtonLoader } from '../../../../../../utils/ButtonLoader'
import { PiCopySimpleFill } from "react-icons/pi";
import { CopyToClipBoard } from '../../../../../../utils/copyToClipBoard'
import { getOrCreateWallet } from '../../../../../../../apis/wallet'


export const FundwalletWithCrypto = ({ closeModal, wallet, selectedNetwork }) => {

  const { data: walletDetailQuery, isPending } = useQuery({
    queryKey: ["getOrCreateWallet", wallet?.currency?.code, selectedNetwork],
    queryFn: () => getOrCreateWallet(wallet?.currency?.code, { network_type: selectedNetwork })
  })

  console.log("wa", walletDetailQuery)

  const firstVirtualBankAccount = walletDetailQuery?.results?.[0]

  console.log("lorem", firstVirtualBankAccount)

  const { handleCopyToClipBoardFunction } = CopyToClipBoard({ successMessage: "Account Number Copied" })

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[600px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
      >

        {isPending ? (
          <ButtonLoader />
        ) : (

          <div className='md:pb-8'>

            <div className='flex items-center justify-center text-center flex-col'>

              <div className='relative w-[120px] sm:mb-4'>
                <OnboardingLogo path={"/images/logo/logoblue.png"} />
              </div>

              <DashboardHeaderContent 
                header={`Fund your ${wallet?.currency?.code} Wallet`} 
                subheader="Transfer money to the Account below to fund this wallet"
                headerClassName={"text-base sm:text-[24px]"}
              />

            </div>

            <div className="space-y-8 md:px-10 text-base pt-5">

              <div className="flex items-center justify-between text-xs sm:text-sm">
                <div className='text-dashboard_sidebar_color'>Wallet Address</div>
                <div className='flex gap-2 items-center cursor-pointer' onClick={() => handleCopyToClipBoardFunction(firstVirtualBankAccount?.account_number)}>
                  {firstVirtualBankAccount?.account_number} <PiCopySimpleFill />
                </div>
              </div>

              <div className="flex items-center justify-between text-xs sm:text-sm">
                <div className='text-dashboard_sidebar_color'>Network</div>
                <div>{selectedNetwork || firstVirtualBankAccount?.account_name}</div>
              </div>

              <div className="flex items-center justify-between text-xs sm:text-sm">
                <div className='text-dashboard_sidebar_color'>Fee</div>
                <div>{firstVirtualBankAccount?.bank_name}</div>
              </div>

              <div className="pt-10">
                <a href={"/dashboard/wallet"}>
                  <Button
                    type='submit'
                    className={`p-3 px-6 btn flex items-center justify-center gap-2 font-md text-xs ${'btn-opacity'}`}
                  >
                    <span>I have transfered</span>
                  </Button>
                </a>
              </div>

            </div>


          </div>

        )}




      </Modal>
    </>
  )
}
