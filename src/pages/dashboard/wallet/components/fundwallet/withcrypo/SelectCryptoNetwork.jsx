import React, { useState } from 'react'
import Modal from '../../../../../../components/modal/Modal'
import { useQuery } from '@tanstack/react-query'
import { <PERSON><PERSON><PERSON>oa<PERSON> } from '../../../../../../utils/ButtonLoader'
import { HiMiniCheckCircle } from 'react-icons/hi2'
import { GoCircle } from 'react-icons/go'
import Button from '../../../../../../components/button/button'
import { useCallModal } from '../../../../../../hooks/useCallModal'
import { FundwalletWithCrypto } from './FundwalletWithCrypto'
import { currencycodeApi } from '../../../../../../../apis/currency'

const SelectCryptoNetwork = ({ closeModal, wallet }) => {
  const [selectedNetwork, setSelectedNetwork] = useState(null)
  const { isOpenModal, openModal, closeModal: closeNetworkModal } = useCallModal()

  const { data: networklist, isPending: isLoadingNetwork } = useQuery({
    queryKey: ["getallcryptonetworks", wallet?.currency?.code],
    queryFn: () => currencycodeApi(wallet?.currency?.code),
  })

  const handleSelectNetwork = (network) => {
    // If the same option is clicked again, deselect it
    setSelectedNetwork(selectedNetwork === network ? null : network)
  }

  const handleProceed = () => {
    openModal()
  }

  // Filter out duplicate network names
  const networkNames = networklist?.crypto_network_list?.map(network => network.network_name) || []
  const uniqueNetworkNames = [...new Set(networkNames)]
  const filteredNetworkName = networklist?.crypto_network_list?.filter(
    (network, index) => networkNames.indexOf(network.network_name) === index
  ) || []

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Select Network"
      >
        <div>
          {isLoadingNetwork ? (
            <ButtonLoader />
          ) : networklist?.crypto_network_list?.length < 1 ? (
            <div className='text-xs text-lightblack flex items-center text-center leading-5'>
              There are no available network for this currency.
            </div>
          ) : (
            <div className="flex flex-col gap-4 sm:p-8">
              {filteredNetworkName.map((network) => (
                <div
                  key={network.id}
                  onClick={() => handleSelectNetwork(network?.network_name)}
                  className={`cursor-pointer border ${selectedNetwork === network?.network_name && 'border-auth_bg bg-gray-100'} w-full rounded-xl px-4 p-2 flex items-center justify-between`}
                >
                  <div className="flex flex-col w-[70%]">
                    <div className="font-semibold text-sm capitalize">{network?.network_name}</div>
                  </div>
                  <div className="flex items-center">
                    {selectedNetwork === network?.network_name ? (
                      <HiMiniCheckCircle className="text-auth_bg" />
                    ) : (
                      <GoCircle />
                    )}
                  </div>
                </div>
              ))}

              <div className="mt-4">
                <Button
                  disabled={selectedNetwork === null}
                  onClick={handleProceed}
                  className="p-3 btn flex items-center justify-center gap-2 font-md text-xs"
                >
                  Proceed
                </Button>
              </div>
            </div>
          )}
        </div>
      </Modal>

      {isOpenModal && (
        <FundwalletWithCrypto 
          closeModal={closeNetworkModal} 
          wallet={wallet} 
          selectedNetwork={selectedNetwork}
        />
      )}
    </>
  )
}

export default SelectCryptoNetwork