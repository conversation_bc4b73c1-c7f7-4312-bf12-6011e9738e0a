import React from 'react'
import Modal from '../../../../../../components/modal/Modal'
import Button from '../../../../../../components/button/button';
import { Link, useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';

const SuccessFundWithCard = ({successResponse}) => {
  console.log("sur", successResponse)

  const queryClient = useQueryClient()
  queryClient.invalidateQueries(["getwallet"])

  return (
    <Modal
      width="w-[90%] sm:max-w-[450px]"
      position="modal-center"
      // onClose={closeModal}
      showCloseButton={false}
      title={`${successResponse?.data?.currency?.code} Funded Sussessfully`}
    >
      <div className='flex flex-col'>
        <div className="text-center">
          <div className='py-5 flex items-center justify-center'>
            <img alt='img' src="/images/vectors/success.gif" width={100} height={100} />
          </div>

          <p className='text-[14px] font-[500]'>{successResponse?.data?.currency?.code} Funded Successfully</p>
          <p className='text-xs font-[300]'>You have successfully fund your <span className='font-medium'>{successResponse?.data?.currency?.code}</span> wallet with {successResponse?.data?.currency?.symbol}{parseFloat(successResponse?.data?.amount).toLocaleString()}.</p>

          <div className='mt-10'>
            <a href="/dashboard/wallet">
            <Button
              // onClick={() => navigate("/dashboard/wallet")}
              className={"w-full p-3 btn flex items-center justify-center gap-2 font-md text-xs"}
            >
              <span>Back to See All Wallets</span>
            </Button>
            </a>

          </div>

        </div>
      </div>
    </Modal>
  )
}

export default SuccessFundWithCard;