import { useState } from 'react';
import Modal from '../../../../../../components/modal/Modal';
import Button from '../../../../../../components/button/button';
import { useCallModal } from '../../../../../../hooks/useCallModal';
import Input from '../../../../../../components/input/input';
import { useMutation, useQuery } from '@tanstack/react-query';
import { fundWallet } from '../../../../../../../apis/wallet';
import toast from 'react-hot-toast';
import { ButtonLoader } from '../../../../../../utils/ButtonLoader';
import FundWithCard from './FundWithCard';
import { getAllCards } from '../../../../../../../apis/card';
import { FormatNumberWithCommas } from '../../../../../../utils/FormatNumberWithCommas';

const EnterAmount = ({ closeModal, wallet }) => {
  const { isOpenModal, openModal, closeModal: closeCardListModal } = useCallModal();
  const [amount, setAmount] = useState('');

  const { data: successResponse, mutateAsync, isPending } = useMutation({
    mutationKey: 'fundwallet',
    mutationFn: fundWallet,
  });

  const { data: userCardsQuery, isPending: isFetchingUserCards } = useQuery({
    queryKey: ['getallusercards'],
    queryFn: () => getAllCards({ is_virtual: false }),
  });

  let domain = window.location.host;
  let protocol = window.location.protocol;
  const callBackUrl = `${protocol}//${domain}/dashboard/wallet?transaction-type=fund-wallet`;


  const handleAmountChange = (e) => {
    const inputValue = e.target.value;

    // If backspace is pressed and input is empty, allow it
    if (inputValue === '') {
      setAmount('');
      return;
    }

    // Remove commas before processing
    const unformattedValue = inputValue.replace(/,/g, '');

    // Check if it's a valid number
    if (!isNaN(unformattedValue)) {
      setAmount(unformattedValue);
    }
  };

  const handleFundWallet = async (e) => {
    e.preventDefault();

    const apiPayload = {
      currency: wallet.currency.code,
      amount: amount,
      pay_with: 'card',
      callback_url: callBackUrl,
    };

    if (userCardsQuery?.count === 0) {
      // No cards available, proceed directly to payment URL
      try {
        const response = await mutateAsync(apiPayload);

        if (response && response.paid === false) {
          window.location.href = response.payment_link;
        }
      } catch (error) {
        console.log('error', error);
        toast.error(error.detail || 'An error occurred');
      }
    } else {
      // Cards are available, open the FundWithCard modal
      openModal();
    }
  };

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Fund with card"
      >
        <form onSubmit={handleFundWallet} className="overflow-hidden">
          <div className="flex flex-col mx-auto justify-center w-full .md:w-[300px]">
            <div className="text-sm text-start font-[400] py-2">Enter Amount</div>

            <Input
              type="text"
              value={amount ? FormatNumberWithCommas(amount) : ''}
              onChange={handleAmountChange}
              placeholder="Enter Amount"
              className="p-3"
              inputMode="numeric"
              // pattern="[0-9]*"
              min="0"
            />

            <div className="mt-10">
              <Button
                type="Submit"
                disabled={!amount || isPending}
                className={`p-3 px-6 btn flex items-center justify-center gap-2 font-md text-xs ${!amount || (isPending && 'btn-opacity')
                  }`}
              >
                {isPending ? (
                  <ButtonLoader />
                ) : (
                  <>
                    <span>Proceed</span>
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </Modal>

      {isOpenModal && (
        <FundWithCard
          userCardsQuery={userCardsQuery}
          closeModal={closeCardListModal}
          isPending={isPending}
          withCard={mutateAsync}
          wallet={wallet}
          enteredAmount={amount}
          successResponse={successResponse}
        />
      )}
    </>
  );
};

export default EnterAmount;
