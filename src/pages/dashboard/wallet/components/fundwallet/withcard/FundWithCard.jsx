import React, { useState } from 'react';
import Modal from '../../../../../../components/modal/Modal';
import Button from '../../../../../../components/button/button';
import { HiMiniCheckCircle } from 'react-icons/hi2';
import { GoCircle } from 'react-icons/go';
import { ButtonLoader } from '../../../../../../utils/ButtonLoader';
import { useCallModal } from '../../../../../../hooks/useCallModal';
import toast from 'react-hot-toast';
import SuccessFundWithCard from './SuccessFundWithCard';

const FundWithCard = ({ userCardsQuery, withCard, closeModal, wallet, enteredAmount, isPending, successResponse }) => {
  const [selectedCard, setSelectedCard] = useState(null);

  const { isOpenModal, openModal } = useCallModal();

  const handleCardSelect = (cardId) => {
    setSelectedCard(selectedCard === cardId ? null : cardId);
  };

  const handleFundWithCard = async (e, cardId) => {
    e.preventDefault();

    const apiPayload = {
      currency: wallet.currency.code,
      amount: enteredAmount,
      pay_with: 'card',
      card: selectedCard, // Include the selected card ID
    };

    try {
      const response = await withCard(apiPayload);
      console.log("res", response)
      // Handle success (e.g., show success modal)
      if (response) {
        openModal(); // Open the success modal
      }
    } catch (error) {
      console.log('error', error);
      toast.error(error.detail || 'An error occurred');
    }
  };


  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Select card"
      >
        <div>
          <div className="rounded-xl border p-4 space-y-4">
            {userCardsQuery?.results?.map((item) => {
              const { last_4_digits, card_type, exp_month, exp_year, bank, id, image } = item;

              return (
                <div
                  key={id}
                  onClick={() => handleCardSelect(id)}
                  className={`${selectedCard === id ? 'bg-gray-100 border-auth_bg' : ''
                    } hover:bg-gray-100 transition-all duration-500 cursor-pointer border w-full rounded-xl p-5 flex items-center justify-between`}
                >
                  <div className="flex items-center gap-3 w-[70%]">
                    <img
                      src={image ? image : '/images/logo/faviconblue.png'}
                      className="w-[30px] h-[30px] rounded-full"
                    />
                    <div className="flex flex-col justify-center gap-1">
                      <h5 className="capitalize text-xs text-black">
                        {card_type} ending with {last_4_digits}
                      </h5>
                      <p className="text-xs text-gray-500">
                        Expiry {exp_month}/{exp_year}
                      </p>
                    </div>
                  </div>
                  <div>
                    {selectedCard === id ? (
                      <HiMiniCheckCircle className="text-auth_bg" />
                    ) : (
                      <GoCircle />
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          <div className="mt-6">
            <Button
              type="Submit"
              disabled={!selectedCard || isPending}
              onClick={handleFundWithCard}
              className={'p-3 px-6 btn flex items-center justify-center gap-2 font-md text-xs'}
            >
              {isPending ? (
                <ButtonLoader />
              ) : (
                <>
                  <span>Proceed</span>
                </>
              )}
            </Button>
          </div>
        </div>
      </Modal>

      {(isOpenModal && successResponse) && (
        <SuccessFundWithCard successResponse={successResponse} />
      )}
    </>

  );
};

export default FundWithCard;