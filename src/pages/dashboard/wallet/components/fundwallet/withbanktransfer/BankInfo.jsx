import React from 'react'
import Modal from '../../../../../../components/modal/Modal'
import { DashboardHeaderContent, OnboardingLogo } from '../../../../../../utils/Utils'
import { useQuery } from '@tanstack/react-query'
import Button from '../../../../../../components/button/button'
import { ButtonLoader } from '../../../../../../utils/ButtonLoader'
import { PiCopySimpleFill } from "react-icons/pi";
import { CopyToClipBoard } from '../../../../../../utils/copyToClipBoard'
import { bankAccount } from '../../../../../../../apis/account'


export const BankInfo = ({ closeModal }) => {

  const { data, isPending } = useQuery({
    queryKey: ["getbankAccount"], queryFn: () => bankAccount({ is_virtual: true })
  })

  // const navigate = useNavigate()

  const firstVirtualBankAccount = data?.results?.[0]

  console.log("info", firstVirtualBankAccount)
  console.log("bank", data)

  const { handleCopyToClipBoardFunction } = CopyToClipBoard({ successMessage: "Account Number Copied" })

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[600px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
      >

        {isPending ? (
          <ButtonLoader />
        ) : (

          <div className='md:pb-8'>

            <div className='flex items-center justify-center text-center flex-col'>

              <div className='relative w-[140px] mb-6'>
                <OnboardingLogo path={"/images/logo/logoblue.png"} />
              </div>

              <DashboardHeaderContent header="Pay with bank transfer" subheader="Transfer money to the Account below to fund this wallet" />

            </div>

            <div className="space-y-8 md:px-10 text-base pt-5">

              <div className="flex items-center justify-between text-xs sm:text-sm">
                <div className='text-dashboard_sidebar_color'>Account Number</div>
                <div className='flex gap-2 items-center cursor-pointer' onClick={() => handleCopyToClipBoardFunction(firstVirtualBankAccount?.account_number)}>{firstVirtualBankAccount?.account_number} <PiCopySimpleFill /></div>
              </div>

              <div className="flex items-center justify-between text-xs sm:text-sm">
                <div className='text-dashboard_sidebar_color'>Account Name</div>
                <div>{firstVirtualBankAccount?.account_name}</div>
              </div>

              <div className="flex items-center justify-between text-xs sm:text-sm">
                <div className='text-dashboard_sidebar_color'>Bank Name</div>
                <div>{firstVirtualBankAccount?.bank_name}</div>
              </div>

              <div className="pt-10">
                <a href={"/dashboard/wallet"}>
                  <Button
                    type='submit'
                    onClick={(e) => {
                      e.preventDefault(); closeModal();
                    }}
                    className={`p-3 px-6 btn flex items-center justify-center gap-2 font-md text-xs ${'btn-opacity'}`}
                  >
                    <span>I have transfered</span>

                  </Button>
                </a>

              </div>

            </div>


          </div>

        )}

      </Modal>
    </>
  )
}