import React, { useMemo, useState } from 'react'
import Modal from '../../../../components/modal/Modal';
import { Select } from 'antd';
import { useMutation, useQuery } from '@tanstack/react-query';
import { getAllCurrencies } from '../../../../../apis/currency';
import Button from '../../../../components/button/button';
import { ButtonLoader, PageLoader } from '../../../../utils/ButtonLoader';
import { createNewWallet } from '../../../../../apis/wallet';
import Successwallet from './Successwallet';
import { useCallModal } from '../../../../hooks/useCallModal';
import toast from 'react-hot-toast';
import useCurrencySearch from '../../../../hooks/useCurrencySearch';
import { currencycodeApi } from '../../../../../apis/currency';

const Addwallet = ({ closeModal }) => {
  const { isOpenModal, openModal, closeModal: closeSuccessModal } = useCallModal();
  const [selectedCurrency, setSelectedCurrency] = useState(null);
  const [selectedNetwork, setSelectedNetwork] = useState(null);
  const [selectedCurrencyDetails, setSelectedCurrencyDetails] = useState(null);

  // Query for network list when a crypto currency is selected
  const { data: networklist, isPending: isLoadingNetwork } = useQuery({
    queryKey: ["getallcryptonetworks", selectedCurrency],
    queryFn: () => currencycodeApi(selectedCurrency),
    enabled: !!selectedCurrency && !!selectedCurrencyDetails?.is_crypto
  });

  const handleCurrencyChange = (value) => {
    setSelectedCurrency(value);
    setSelectedNetwork(null); // Reset network selection when currency changes
    
    // Find and store the selected currency details
    const currency = allCurrencyInfiniteData?.pages.flatMap((page) =>
      page.results.find((item) => item.code === value)
    )[0];
    setSelectedCurrencyDetails(currency);
  };

  const handleNetworkChange = (value) => {
    setSelectedNetwork(value);
  };

  // add new wallet
  const { mutateAsync, data, isPending: isCreating } = useMutation({ 
    mutationFn: createNewWallet,
    onSuccess: () => {
      openModal();
    },
    onError: (error) => {
      toast.error(error.detail);
    }
  });

  const handleAddNewWallet = async (e) => {
    e.preventDefault();
    try {
      const payload = {
        currency: selectedCurrency,
        ...(selectedCurrencyDetails?.is_crypto && { network_code: selectedNetwork })
      };
      await mutateAsync(payload);
    } catch (error) {
      console.log("error", error);
    }
  };

  const {
    searchTerm,
    setSearchTerm,
    setIsSearching,
    isSearching,
    allCurrencyInfiniteData,
    isCurrencyFetching,
    isFetchingNextCurrencyPage,
    debouncedSearch,
    handleScroll,
  } = useCurrencySearch('');

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Add new wallet"
      >
        <form onSubmit={handleAddNewWallet} className='overflow-hidden'>
          <div className='flex flex-col mx-auto justify-center w-full .md:w-[300px]'>
            <div className='text-sm text-start font-[400] py-2'>Currency</div>
            <div>
              <Select
                size="large"
                className="w-full .md:w-[500px] placeholder:text-[12px]"
                showSearch
                placeholder="Select Currency"
                optionFilterProp="label"
                autoFocus
                onChange={handleCurrencyChange}
                onSearch={(value) => {
                  setIsSearching(true);
                  debouncedSearch(value);
                }}
                filterOption={false}
                options={
                  allCurrencyInfiniteData?.pages.flatMap((page) =>
                    page.results.map((item) => ({
                      key: item.id,
                      value: item.code,
                      label: `${item.name} (${item.code})`,
                    }))
                  ) || []
                }
                onPopupScroll={handleScroll}
                notFoundContent={
                  isCurrencyFetching || isSearching ? (
                    <div className="flex justify-center p-2">
                      <PageLoader size={20} color="#2A4365" />
                    </div>
                  ) : null
                }
                dropdownRender={(menu) => (
                  <>
                    {menu}
                    {isFetchingNextCurrencyPage && (
                      <div className="flex justify-center p-2">
                        <PageLoader size={20} color="#2A4365" />
                      </div>
                    )}
                  </>
                )}
              />
            </div>

            {/* Show network selection for crypto currencies */}
            {selectedCurrencyDetails?.is_crypto && (
              <div className='mt-4'>
                <div className='text-sm text-start font-[400] py-2'>Network Type</div>
                <Select
                  size="large"
                  className="w-full placeholder:text-[12px]"
                  showSearch
                  placeholder="Select Network"
                  optionFilterProp="label"
                  onChange={handleNetworkChange}
                  loading={isLoadingNetwork}
                  value={selectedNetwork}
                  options={networklist?.crypto_network_list?.map((network) => ({
                    label: <div className="capitalize">{network.network_name}</div>,
                    value: network.network,
                  })) || []}
                />
              </div>
            )}

            <div className='mt-10'>
              <Button
                type='Submit'
                disabled={!selectedCurrency || (selectedCurrencyDetails?.is_crypto && !selectedNetwork)}
                className={"p-3 btn flex items-center justify-center gap-2 font-md text-xs"}
              >
                {isCreating ? (
                  <ButtonLoader />
                ) : (
                  <span>Add wallet</span>
                )}
              </Button>
            </div>
          </div>
        </form>
      </Modal>

      {isOpenModal && data && (
        <Successwallet 
          selectedCurrency={selectedCurrency} 
          closeModal={closeSuccessModal}
          closeParentModal={closeModal} // Pass the parent modal close function
        />
      )}
    </>
  );
};

export default Addwallet;
