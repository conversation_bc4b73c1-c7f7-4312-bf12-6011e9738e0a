import React from 'react'
import Modal from '../../../../components/modal/Modal'
import { useCallModal } from '../../../../hooks/useCallModal';
import Button from '../../../../components/button/button';
import { useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';

const Successwallet = ({ closeModal, selectedCurrency, closeParentModal }) => {  // Add closeParentModal prop
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const handleViewWallet = () => {
    // Close both modals
    closeModal();
    closeParentModal();
    
    // Navigate to wallet page
    navigate("/dashboard/wallet");
    
    // Invalidate the wallet query to ensure fresh data
    queryClient.invalidateQueries(["getwallet"]);
  };

  return (
    <Modal
      width="w-[90%] sm:max-w-[450px]"
      position="modal-center"
      onClose={closeModal}
      showCloseButton={true}
      title="Added Successfully"
    >
      <div className='flex flex-col'>
        <div className="text-center">
          <div className='py-5 flex items-center justify-center'>
            <img alt='img' src="/images/vectors/success.gif" width={100} height={100} />
          </div>

          <p className='text-[14px] font-[500]'>Added Successfully</p>
          <p className='text-xs font-[300]'>You successfully added <span className='font-semibold'>{selectedCurrency}</span> wallet.</p>

          <div className='mt-10'>
            <Button
              onClick={handleViewWallet}
              className={"w-full p-3 btn flex items-center justify-center gap-2 font-md text-xs"}
            >
              <span>View Wallet</span>
            </Button>
          </div>

        </div>
      </div>
    </Modal>
  )
}

export default Successwallet
