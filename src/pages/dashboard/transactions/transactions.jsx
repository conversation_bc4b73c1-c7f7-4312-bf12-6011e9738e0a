import { DashboardHeaderContent} from '../../../utils/Utils'
import { useState, useEffect } from 'react'
import { FaArrowLeft } from "react-icons/fa6";
import { CiSearch } from "react-icons/ci";
import { PiSlidersFill } from "react-icons/pi";
import { useQuery } from '@tanstack/react-query'
import { getAllTransaction } from '../../../../apis/transactions'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import TransactionTable from './components/Transactiontable';
import { useCallModal } from '../../../hooks/useCallModal';
import FilterTransaction from './components/FilterTransaction';


const Transactions = () => {
  const navigate = useNavigate();
  const { currency_code } = useParams();
  const { closeModal, isOpenModal, openModal } = useCallModal();
  const [searchParams, setSearchParams] = useSearchParams();
  
  // State for search query
  const [searchQuery, setSearchQuery] = useState('');
  
  // State for API filter params
  const [filterParams, setFilterParams] = useState({});

  // Combine all filter parameters
  const getQueryParams = () => {
    const params = { ...filterParams };
    
    // Add currency code if available from URL params
    if (currency_code && !params.currency_code) {
      params.currency_code = currency_code;
    }
    
    // Add search query as reference if available
    if (searchQuery) {
      params.reference = searchQuery;
    }
    
    return params;
  };

  // Fetch transactions with filters
  const { data: transactionQuery, isPending, refetch } = useQuery({ 
    queryKey: ["getalltransactions", getQueryParams()], 
    queryFn: () => getAllTransaction(getQueryParams())
  });

  // Handle navigation back
  const handleNavigateback = (e) => {
    e.preventDefault();
    navigate(-1);
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Apply filters from the filter modal
  const applyFilters = (filters) => {
    setFilterParams(filters);
    
    // Update URL search params to reflect filters
    const newSearchParams = new URLSearchParams();
    
    // Add existing search if present
    if (searchQuery) newSearchParams.set('reference', searchQuery);
    
    // Add new filters to URL
    Object.entries(filters).forEach(([key, value]) => {
      if (value) newSearchParams.set(key, value);
    });
    
    setSearchParams(newSearchParams);
  };

  // Effect to refetch when filters change
  useEffect(() => {
    refetch();
  }, [filterParams, searchQuery, refetch]);

  // Effect to initialize filters from URL on component mount
  useEffect(() => {
    const params = {};
    const search = searchParams.get('reference');
    const amountFrom = searchParams.get('amount_from');
    const amountTo = searchParams.get('amount_to');
    const transactionType = searchParams.get('transaction_type');
    const status = searchParams.get('status');
    const dateFrom = searchParams.get('date_from');
    const dateTo = searchParams.get('date_to');
    const currencyCode = searchParams.get('currency_code');
    
    if (search) setSearchQuery(search);
    
    if (amountFrom) params.amount_from = amountFrom;
    if (amountTo) params.amount_to = amountTo;
    if (transactionType) params.transaction_type = transactionType;
    if (status) params.status = status;
    if (dateFrom) params.date_from = dateFrom;
    if (dateTo) params.date_to = dateTo;
    if (currencyCode) params.currency_code = currencyCode;
    
    if (Object.keys(params).length > 0) {
      setFilterParams(params);
    }
  }, [searchParams]);

  return (

    <>
    <div>

      <div className='flex items-center justify-start'>
        <div className='flex items-center gap-3 mb-5 text-auth_bg cursor-pointer' onClick={handleNavigateback}>
          <FaArrowLeft />
          <span className='text-[13px]'>Go Back</span>
        </div>
      </div>

      <DashboardHeaderContent header="Transactions" subheader="This page provides a comprehensive list of all transactions." />

      <div className="shadow-sm border rounded-lg border-border_color pt-5">

        <div className='flex items-center justify-between flex-col md:flex-row gap-5 px-4'>
          <div className="p-2 border rounded-[8px] text-[#404850] text-[14px] flex items-center gap-2 w-full md:w-80">
            <CiSearch />
            <input 
              type="search" 
              placeholder='Search' 
              className='w-full border-none outline-none focus:ring-0'
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>
          <div className='flex items-center gap-4 w-full md:w-fit'>
            <div 
              className='flex items-center rounded-[8px] border p-2 px-3 gap-2 cursor-pointer'
              onClick={()=>openModal(true)}
            >
              <span className='text-sm text-[#404850]'>Filter</span>
              <PiSlidersFill />
              {/* {Object.keys(filterParams).length > 0 && (
                <span className="bg-auth_bg text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {Object.keys(filterParams).length}
                </span>
              )} */}
            </div>
          </div>
        </div>

        <TransactionTable 
          transactions={transactionQuery} 
          isPending={isPending}
        />

      </div>

    </div>

    {isOpenModal && (
      <FilterTransaction
        closeModal={closeModal}
        applyFilters={applyFilters}
      />
    )}
    </>

  )
}

export default Transactions;
