import React from 'react';
import { useNavigate } from 'react-router-dom';
import { formatDate, getStatusColor } from '../../../../utils/Utils';
import { IoEyeOutline, IoCopyOutline } from 'react-icons/io5';
import { CopyToClipBoard } from '../../../../utils/copyToClipBoard';
import GenericTable from '../../../../components/table/GenericTable';

const TransactionTable = ({ transactions, isPending }) => {
  const navigate = useNavigate();
  const { handleCopyToClipBoardFunction } = CopyToClipBoard({ successMessage: "Reference number copied to clipboard!" });

  // Define columns for the transaction table
  const columns = [
    {
      key: 'reference',
      header: 'Reference',
      render: (item) => (
        <div className="break-words relative group">
          <span className="truncate block max-w-[60px] sm:max-w-[100px]">{item.reference}</span>
          <button
            className="absolute right-0 top-0 bg-gray-200 text-auth_bg p-1 rounded-full hover:bg-auth_bg hover:text-white transition-colors opacity-0 group-hover:opacity-100"
            title="Copy reference"
            onClick={(e) => {
              e.stopPropagation();
              handleCopyToClipBoardFunction(item.reference);
            }}
          >
            <IoCopyOutline size={12} />
          </button>
        </div>
      )
    },
    {
      key: 'transaction_type',
      header: 'Type',
      render: (item) => (
        <div className='flex items-center gap-1 sm:gap-2 capitalize'>
          <img src={item?.service?.icon || "/images/logo/faviconblue.png"} className='rounded-full hidden sm:block' alt='icon' width={20} height={20} />
          <span className="truncate max-w-[60px] sm:max-w-full">{item.transaction_type}</span>
        </div>
      )
    },
    {
      key: 'created_on',
      header: 'Date',
      render: (item) => formatDate(item.created_on)
    },
    {
      key: 'currency_code',
      header: 'Currency'
    },
    {
      key: 'amount',
      header: 'Amount',
      render: (item) => (
        <span className="whitespace-nowrap">
          {item?.currency_symbol}{parseFloat(item.amount).toLocaleString()}
        </span>
      )
    },
    {
      key: 'status',
      header: 'Status',
      render: (item) => (
        <span style={{ color: getStatusColor(item?.status) }} className="capitalize">
          {item.status}
        </span>
      )
    },
    {
      key: 'actions',
      header: 'Details',
      sortable: false,
      render: () => (
        <div className='flex items-center gap-1 sm:gap-2 cursor-pointer text-auth_bg text-xs font-semibold'>
          <IoEyeOutline size={16} /> <span className="hidden sm:inline">View</span>
        </div>
      )
    }
  ];

  // Handle row click to navigate to transaction details
  const handleRowClick = (item) => {
    navigate(`/dashboard/transactions/${item?.transaction_type}/${item?.id}`);
  };

  console.log("trans", transactions)

  return (
    <GenericTable
      columns={columns}
      data={transactions}
      isPending={isPending}
      emptyMessage="No available transaction at the moment"
      itemsPerPage={10}
      onRowClick={handleRowClick}
    />
  );
};

export default TransactionTable;
