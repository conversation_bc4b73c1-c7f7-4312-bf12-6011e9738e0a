import { useState } from 'react'
import Modal from '../../../../components/modal/Modal'
import Input from '../../../../components/input/input'
import Button from '../../../../components/button/button'
import { Select } from 'antd'
import { PageLoader } from '../../../../utils/ButtonLoader'
import useCurrencySearch from '../../../../hooks/useCurrencySearch'

// Custom styles for antd Select placeholder
const selectStyles = {
  placeholder: {
    fontSize: '12px'
  }
};

const FilterTransaction = ({ closeModal, applyFilters }) => {
  // Filter state
  const [filters, setFilters] = useState({
    amount_from: '',
    amount_to: '',
    transaction_type: '',
    status: '',
    date_from: '',
    date_to: '',
    currency_code: ''
  })

  const {
    searchTerm,
    setSearchTerm,
    setIsSearching,
    isSearching,
    allCurrencyInfiniteData,
    isCurrencyFetching,
    isFetchingNextCurrencyPage,
    debouncedSearch,
    handleScroll,
  } = useCurrencySearch('');

  // Transaction type options
  const transactionTypeOptions = [
    { value: 'fund wallet', label: 'Fund Wallet' },
    { value: 'internal transfer', label: 'Internal Transfer' },
    { value: 'external transfer', label: 'External Transfer' },
    { value: 'swap', label: 'Swap' },
    { value: 'bonus', label: 'Bonus' },
    { value: 'bill payment', label: 'Bill Payment' },
    { value: 'airtime to cash', label: 'Airtime To Cash' },
    { value: 'subscription payment', label: 'Subscription Payment' },
    { value: 'product order', label: 'Product Order' }, 
    { value: 'transaction fee', label: 'Transaction Fee' },
    { value: 'airtime to cash fee', label: 'Airtime To Cash Fee' },
  ]

  // Status options
  const statusOptions = [
    { value: 'successful', label: 'Successful' },
    { value: 'pending', label: 'Pending' },
    { value: 'failed', label: 'Failed' },
    { value: 'processing', label: 'Processing' },
    { value: 'cancelled', label: 'Cancelled' },
    { value: 'refunded', label: 'Refunded' },
    { value: 'fraudulent', label: 'Fraudulent' }
  ]

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFilters(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Handle select changes
  const handleSelectChange = (value, name) => {
    setFilters(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Handle currency change
  const handleCurrencyChange = (value) => {
    handleSelectChange(value, 'currency_code')
  }

  // Handle apply filter
  const handleApplyFilter = () => {
    // Create query params object, only including non-empty values
    const queryParams = {}
    
    if (filters.amount_from) queryParams.amount_from = filters.amount_from
    if (filters.amount_to) queryParams.amount_to = filters.amount_to
    if (filters.transaction_type) queryParams.transaction_type = filters.transaction_type
    if (filters.status) queryParams.status = filters.status
    if (filters.date_from) queryParams.date_from = filters.date_from
    if (filters.date_to) queryParams.date_to = filters.date_to
    if (filters.currency_code) queryParams.currency_code = filters.currency_code
    
    // Pass the filters to parent component
    applyFilters(queryParams)
    closeModal()
  }

  // Handle reset filters
  const handleResetFilters = () => {
    setFilters({
      amount_from: '',
      amount_to: '',
      transaction_type: '',
      status: '',
      date_from: '',
      date_to: '',
      currency_code: ''
    })
  }

  return (
    <Modal
      width="w-[90%] sm:max-w-[450px]"
      position="modal-center"
      onClose={closeModal}
      showCloseButton={true}
      title="Filter By"
    >
      <div className="p-4">
        {/* Amount Range */}
        <div className="mb-4">
          <label className="block text-gray-700 text-xs font-medium mb-1">
            Amount
          </label>
          <div className="flex gap-4">
            <div className="w-1/2">
              <Input
                type="number"
                name="amount_from"
                value={filters.amount_from}
                onChange={handleInputChange}
                placeholder="From"
                className="p-2 px-3 w-full"
              />
            </div>
            <div className="w-1/2">
              <Input
                type="number"
                name="amount_to"
                value={filters.amount_to}
                onChange={handleInputChange}
                placeholder="To"
                className="p-2 px-3 w-full"
              />
            </div>
          </div>
        </div>

        {/* Date Range */}
        <div className="mb-4">
          <label className="block text-gray-700 text-xs font-medium mb-1">
            Date
          </label>
          <div className="flex gap-4">
            <div className="w-1/2">
              <Input
                type="date"
                name="date_from"
                value={filters.date_from}
                onChange={handleInputChange}
                className="p-2 px-3 w-full"
              />
            </div>
            <div className="w-1/2">
              <Input
                type="date"
                name="date_to"
                value={filters.date_to}
                onChange={handleInputChange}
                className="p-2 px-3 w-full"
              />
            </div>
          </div>
        </div>

        {/* Currency */}
        <div className="mb-4">
          <label className="block text-gray-700 text-xs font-medium mb-1">
            Currency
          </label>
          <Select
            size="large"
            className="w-full"
            showSearch
            placeholder="Select Currency"
            optionFilterProp="label"
            allowClear
            value={filters.currency_code || undefined}
            onChange={handleCurrencyChange}
            onSearch={(value) => {
              setIsSearching(true);
              debouncedSearch(value);
            }}
            filterOption={false}
            options={
              allCurrencyInfiniteData?.pages.flatMap((page) =>
                page.results.map((item) => ({
                  key: item.id,
                  value: item.code,
                  label: `${item.name} (${item.code})`,
                }))
              ) || []
            }
            onPopupScroll={handleScroll}
            notFoundContent={
              isCurrencyFetching || isSearching ? (
                <div className="flex justify-center p-2">
                  <PageLoader size={20} color="#2A4365" />
                </div>
              ) : null
            }
            dropdownRender={(menu) => (
              <>
                {menu}
                {isFetchingNextCurrencyPage && (
                  <div className="flex justify-center p-2">
                    <PageLoader size={20} color="#2A4365" />
                  </div>
                )}
              </>
            )}
          />
        </div>

        {/* Transaction Type */}
        <div className="mb-4">
          <label className="block text-gray-700 text-xs font-medium mb-1">
            Transaction Type
          </label>
          <Select
            size="large"
            className="w-full"
            placeholder="Enter Transaction Type"
            allowClear
            showSearch
            value={filters.transaction_type || undefined}
            onChange={(value) => handleSelectChange(value, 'transaction_type')}
            options={transactionTypeOptions}
          />
        </div>

        {/* Status */}
        <div className="mb-4">
          <label className="block text-gray-700 text-xs font-medium mb-1">
            Status
          </label>
          <Select
            size="large"
            className="w-full"
            placeholder="Enter Transaction status"
            allowClear
            showSearch
            value={filters.status || undefined}
            onChange={(value) => handleSelectChange(value, 'status')}
            options={statusOptions}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-3 mt-8">
          <Button
            onClick={handleApplyFilter}
            className="p-3 bg-auth_bg text-white hover:bg-auth_bg/90 text-xs"
          >
            Apply Filter
          </Button>
          {Object.values(filters).some(value => value !== '') && (
            <button
              onClick={handleResetFilters}
              className="text-auth_bg text-sm mt-2 hover:underline"
            >
              Reset Filters
            </button>
          )}
        </div>
      </div>
    </Modal>
  )
}

export default FilterTransaction
