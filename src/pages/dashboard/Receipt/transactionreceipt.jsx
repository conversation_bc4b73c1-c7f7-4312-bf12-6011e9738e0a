import React from 'react';
import { DashboardHeaderContent } from '../../../utils/Utils';
import Navigateback from '../../../components/navigateback/navigateback';
import { fetchReceipt } from '../../../../apis/transactions';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'react-router-dom';
import { PageLoader } from '../../../utils/ButtonLoader';
import { FaPrint } from 'react-icons/fa';
import Button from '../../../components/button/button';

export const Transactionreceipt = () => {
  const { id, transaction_type } = useParams();
  const { data, isPending } = useQuery({
    queryKey: ["receiptId", id],
    queryFn: () => fetchReceipt({ transaction_id: id, transaction_type: transaction_type }),
  });

  // Setup the print handler using window.print() directly
  const handlePrint = () => {
    window.print();
  };

  if (isPending) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <PageLoader />
      </div>
    );
  }

  return (
    <div>
      <Navigateback />
      <div className="flex justify-between items-center">
        <DashboardHeaderContent
          header="Transactions Details"
          subheader={
            data ? (
              <div className="text-sm">
                <span className="font-semibold ml-2">{data["Transaction Type"]}</span>
                <span className="font-medium"> | {data["Transaction Date"]}</span>
              </div>
            ) : <></>
          }
          subheaderClassName="!mt-2"
        />
        <div>
          <Button
            onClick={handlePrint}
            className="flex items-center gap-2 p-2 px-4 text-xs"
          >
            <FaPrint />
            <span className='hidden sm:block'>Print Receipt</span>
          </Button>
        </div>
      </div>

      <div
        id="printable-content"
        className="w-full shadow-custom bg-white space-y-6 sm:space-y-10 md:mt-10 rounded-xl border p-4 sm:p-6"
      >
        {/* Add a logo or header for the printed receipt */}
        <div className="print-only text-center mb-4">
          <h1 className="text-xl font-bold">Transaction Receipt</h1>
          <p className="text-sm text-gray-500">Thank you for using our service</p>
        </div>

        {data &&
          Object.entries(data)
            .filter(([key, value]) => value !== null && value !== "")
            .map(([key, value]) => (
              <div key={key} className="grid grid-cols-5 gap-y-2 text-sm">
                <div className="col-span-5 md:col-span-2">
                  <span className='font-semibold'>{key.replace(/_/g, ' ')}</span>
                </div>
                <div className="col-span-5 md:col-span-3 text-lightblack">
                  <div className="w-full break-words overflow-hidden">
                    <span dangerouslySetInnerHTML={{ __html: value }}></span>
                  </div>
                </div>
              </div>
            ))}
      </div>

      {/* Add some CSS for print styling */}
      <style>{`
        @media print {
          body * {
            visibility: hidden;
          }
          #printable-content,
          #printable-content * {
            visibility: visible;
          }
          #printable-content {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            padding: 40px;
            box-shadow: none !important;
            border: none !important;
          }
          .print-only {
            display: block !important;
          }
        }
        @media screen {
          .print-only {
            display: none;
          }
        }
      `}</style>
    </div>
  );
};
