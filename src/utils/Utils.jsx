import { format, parseISO } from 'date-fns';


export const DashboardHeaderContent = ({ header, subheader, headerClassName, subheaderClassName, className }) => {
  return (
    <div className={`md:leading-6 my-4 ${className}`}>
      <div className={`font-[600] text-[24px] capitalize ${headerClassName}`}>{header}</div>
      <div className={`font-[400] text-[13px] ${subheaderClassName}`}>{subheader}</div>
    </div>
  );
};


export const OnboardingLogo = ({ path }) => {
  return (
    <img src={path} alt="Auth Image" layout="fill" objectfit="contain"/>
  )
}


export const TextUnder = ({ header, content }) => {
  return (
    <div className='flex items-center justify-center flex-col'>
      <div className='text-general_text font-[500] text-[24px]'>{header}</div>
      <div className='text-general_gray_text font-[400] text-[14px]'>{content}</div>
    </div>
  )
}

export const generateBreadcrumbs = (path) => {
  if (!path) return [];

  const pathWithoutQuery = path.split('?')[0];
  const segments = pathWithoutQuery.split('/').filter((segment) => segment);

  return segments.map((segment, index) => {
    const href = '/' + segments.slice(0, index + 1).join('/');
    return { label: segment.charAt(0).toUpperCase() + segment.slice(1), href };
  });
};


export const formatDate = (dateString) => {
  try {
    const date = parseISO(dateString); // This should safely parse ISO strings
    return format(date, 'MMM dd, yyyy HH:mm');
  } catch (error) {
    return 'Invalid Date'; // Fallback in case parsing fails
  }
};

export const getStatusColor = (status) => {
  switch (status) { 
    case "successful":
      return "#00B000";
    case "pending":
      return "#FF9D00";
    case "failed":
      return "#F44336";
    default:
      return "#0368FF";                                                                                                                                                                             
  } 
}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         