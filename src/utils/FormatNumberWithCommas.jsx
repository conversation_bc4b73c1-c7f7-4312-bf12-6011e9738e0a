export const FormatNumberWithCommas = (value) => {
    if (!value) return '';
    
    // Remove any existing commas and non-numeric characters except decimal point
    const cleanValue = value.toString().replace(/,/g, '').replace(/[^\d.]/g, '');
    
    // Split into whole and decimal parts
    const [wholePart, decimalPart] = cleanValue.split('.');
    
    // Add commas to whole part
    const formattedWholePart = wholePart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    
    // Combine with decimal part if it exists
    return decimalPart !== undefined ? `${formattedWholePart}.${decimalPart}` : formattedWholePart;
  };