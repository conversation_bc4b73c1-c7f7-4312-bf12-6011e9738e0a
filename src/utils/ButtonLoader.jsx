import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>oa<PERSON> } from "react-spinners"

export const ButtonLoader = () => {
  return (
    <div className="flex items-center justify-center h-full">
      <ClipLoader size={20} color="text-auth_bg" />
    </div>
  )
}


export const PageLoader = ({ size = "20px" }) => {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="flex items-center p-1 justify-center bg-white rounded-full shadow-xl border border-gray-100">
        <ClipLoader size={size} color="#39439E" />
      </div>
    </div>
  )
}