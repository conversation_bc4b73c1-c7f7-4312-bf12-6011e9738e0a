import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const Breadcrumbs = ({ separator, containerClasses, listClasses, activeClasses, capitalizeLinks }) => {
  const { pathname } = useLocation();
  const pathNames = pathname.split('/').filter(path => path); // Use pathname instead of paths

  return (
    <div>
      <ul className={containerClasses}>
        {/* {pathNames.length > 0 && separator} */}
        {pathNames.map((link, index) => {
          let href = `/${pathNames.slice(0, index + 1).join('/')}`;
          let itemClasses = pathname === href ? `${listClasses} ${activeClasses}` : listClasses;
          let itemLink = capitalizeLinks ? link.charAt(0).toUpperCase() + link.slice(1) : link;
          return (
            <React.Fragment key={index}>
              <li className={itemClasses}>
                <Link to={href}>{itemLink}</Link> {/* Change href to to */}
              </li>
              {pathNames.length !== index + 1 && separator}
            </React.Fragment>
          );
        })}
      </ul>
    </div>
  );
};

export default Breadcrumbs;