import React, { useState } from 'react'
import toast from 'react-hot-toast'

export const CopyToClipBoard = ({successMessage}) => {

  const [copied, setCopied] = useState(false)

  const handleCopyToClipBoardFunction = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopied(true),
        toast.success(`${successMessage}`)
      setTimeout(() => setCopied(false), 3000)
    })
  }

  return {
    copied, handleCopyToClipBoardFunction
  }
}