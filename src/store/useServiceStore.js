import { create } from 'zustand';

const useServiceStore = create((set) => ({
  services: null,
  selectedService: null,
  isLoading: false,
  error: null,
  
  setServices: (services) => set({ services }),
  setSelectedService: (service) => set({ selectedService: service }),
  setLoading: (status) => set({ isLoading: status }),
  setError: (error) => set({ error }),
  
  // Helper function to find service by ID or slug
  findServiceById: (serviceId) => {
    const state = useServiceStore.getState();
    return state.services?.results?.find(
      (service) => service.id === serviceId || service.slug === serviceId
    );
  },

  // Reset store
  reset: () => set({ 
    services: null, 
    selectedService: null, 
    isLoading: false, 
    error: null 
  }),
}));

export default useServiceStore;