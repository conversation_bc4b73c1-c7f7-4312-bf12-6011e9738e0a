import { RiExchangeBoxFill } from "react-icons/ri";
import { TbChartBubbleFilled, TbCreditCard, TbCreditCardFilled } from "react-icons/tb";
import { RiWallet3Fill } from "react-icons/ri";
import { BsDatabase } from "react-icons/bs";




export const Headercontent = [
  {
    name: "Dashboard",
    path: "/dashboard",
    icon: <TbChartBubbleFilled size={16} />
  },

  {
    name: "Wallets",
    path: "/dashboard/wallet",
    icon: <RiWallet3Fill size={16} />
  },

  {
    name: "Cards",
    path: "/dashboard/cards",
    icon: <TbCreditCard size={16} />
  },

  // {
  //   name: "Transfer",
  //   icon: <RiExchangeBoxFill size={16} />,
  //   dropdown: [
  //     { name: "Internal Transfer", path: "/dashboard/sendmoney?tab=Internal" },
  //     { name: "External Transfer", path: "/dashboard/sendmoney?tab=External" },
  //   ]
  // },
  {
    name: "Transactions",
    path: "/dashboard/transactions",
    icon: <BsDatabase size={16} />
  },
]