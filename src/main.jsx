import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import App from './App.jsx'
import './index.css'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { BrowserRouter } from 'react-router-dom'
import { Toaster } from "react-hot-toast";
import { GoogleOAuthProvider } from '@react-oauth/google'
let C_id = import.meta.env.VITE_APP_GOOGLE_CLIENT_ID


const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
})

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <BrowserRouter>
    <GoogleOAuthProvider clientId={C_id}>
      <QueryClientProvider client={queryClient}>
        <App />
        <Toaster containerClassName="text-xs" toastOptions={{duration: 7000}}/>
      </QueryClientProvider>
    </GoogleOAuthProvider>
    </BrowserRouter>
  </StrictMode>,
)
