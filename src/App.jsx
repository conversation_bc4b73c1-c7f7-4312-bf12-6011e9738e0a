import './App.css'
import { Route, Routes, useLocation, Navigate, Outlet, useNavigate } from 'react-router-dom'
import AuthLayout from './Layouts/authLayout'
import Login from './pages/auth/login/login'
import SignUp from './pages/auth/signup/signup'
import DashboardLayout from './Layouts/dashboardLayout'
import Dashboard from './pages/dashboard/dashboard'
import Wallet from './pages/dashboard/wallet/wallet'
import Transactions from './pages/dashboard/transactions/transactions'
import Sendmoney from './pages/dashboard/sendmoney/sendmoney'
import Notification from './pages/dashboard/notification/notification'
import Myaccount from './pages/dashboard/myaccount/myaccount'
import Helpandsupport from './pages/dashboard/helpandsupport/helpandsupport'
import Currencyexchange from './pages/dashboard/currencyexchange/currencyexchange'
import Cards from './pages/dashboard/cards/cards'
import Billpayment from './pages/dashboard/billpayment/billpayment'
import Verify from './pages/auth/verify/verify'
import Createtransactionpin from './pages/auth/createtransactionpin/createtransactionpin'
import Existingloginuser from './pages/auth/existingloginuser/existingloginuser'
import { Transactionsummary } from './pages/dashboard/Transactionsummary/transactionsummary'
import { Transactionreceipt } from './pages/dashboard/Receipt/transactionreceipt'
import Forgotpassword from './pages/auth/forgotpassword/forgotpassword'
import Createpassword from './pages/auth/createpassword/createpassword'
import Receivemoney from './pages/dashboard/receivemoney/receivemoney'
import Categorybiller from './pages/dashboard/billpayment/categorybiller/Categorybiller'
import { Billpaymentsummary } from './pages/dashboard/billpayment/billpaymentsummary/Billpaymentsummary'
import { Currencyexchangesummary } from './pages/dashboard/currencyexchange/currencyexchangesummary'
import { Transactionsummaryexternal } from './pages/dashboard/Transactionsummary/transactionsummaryexternal'
import Airtimetocash from './pages/dashboard/airtime-to-cash/Airtimetocash'
import Cashrequest from './pages/dashboard/cash-request/cash-request'
import Register from './pages/dashboard/cash-request/register'
import { Welcomepage } from './pages/welcome/Welcomepage'
import { ReceivemoneyLayout } from './pages/dashboard/receivemoney/layout'
import Receivemoneycrypto from './pages/dashboard/receivemoney/receivemoneycrypto'
import Buycrypto from './pages/dashboard/crypto/buycrypto/Buycrypto'
import BuyCryptoLayout from './pages/dashboard/crypto/buycrypto/layout'
import Sendcrypto from './pages/dashboard/sendcrypto/Sendcrypto'
import NotFound from './pages/notfound/NotFound';
import ProtectedAuthRoute from './components/auth/ProtectedAuthRoute'
import { useLayoutEffect } from 'react'
import ServicesVerification from './pages/welcome/components/ServicesVerification'
import useServiceStore from './store/useServiceStore'
import { useServiceNavigation } from './hooks/useServiceNavigation';
import ServiceVerificationGuard from './components/ServiceVerificationGuard';
import AccountTypeVerification from './pages/welcome/components/AccountTypeVerification'

function App() {
  const { pathname } = useLocation();

  useLayoutEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return (
    <>
      <Routes>
        <Route path="*" element={<NotFound />} />

        {/* Auth routes */}
        <Route element={<AuthLayout />}>
          <Route element={<ProtectedAuthRoute />}>
            <Route index path='/' element={<Login />} />
            <Route path='/signup' element={<SignUp />} />
          </Route>
          <Route path='/verify' element={<Verify />} />
          <Route path='/forgotpassword' element={<Forgotpassword />} />
          <Route path='/createpassword' element={<Createpassword />} />
          <Route path='/createtransactionpin' element={<Createtransactionpin />} />
          <Route path='/existing' element={<Existingloginuser />} />
        </Route>

        {/* Dashboard routes */}
        <Route element={<DashboardLayout />}>
          <Route element={<ServiceVerificationGuard />}>
            <Route path='/welcome' element={<Welcomepage />} />
            <Route index path='/dashboard' element={<Dashboard />} />
            <Route index path='/dashboard/services-verification/:slug' element={<ServicesVerification />} />
            <Route index path='/dashboard/account-type-verification' element={<AccountTypeVerification />} />
            <Route index path='/dashboard/cards' element={<Cards />} />

            {/* bill payment */}
            <Route index path='/dashboard/billpayment' element={<Billpayment />} />
            <Route index path='/dashboard/billpayment/:biller' element={<Categorybiller />} />
            <Route index path='/dashboard/billpayment/:biller/summary' element={<Billpaymentsummary />} />

            <Route index path='/dashboard/wallet' element={<Wallet />} />

            <Route path="/dashboard/transactions/">
              <Route index element={<Transactions />} />
              <Route path=":currency_code" element={<Transactions />} />
              <Route path=':transaction_type/:id' element={<Transactionreceipt />} />
            </Route>

            {/* send money */}

            <Route index path='/dashboard/sendmoney' element={<Sendmoney />} />
            <Route index path='/dashboard/sendmoney/transaction-summary' element={<Transactionsummary />} />
            <Route index path='/dashboard/sendmoney/transaction-summary-external' element={<Transactionsummaryexternal />} />

            <Route index path='/dashboard/sendcrypto' element={<Sendcrypto />} />

            {/* receive money */}

            <Route element={<ReceivemoneyLayout />}>
              <Route path='/dashboard/receivemoney' element={<Receivemoney />} />
              <Route path='/dashboard/receivemoney/crypto' element={<Receivemoneycrypto />} />
            </Route>

            <Route element={<BuyCryptoLayout />}>
              <Route path='/dashboard/buycrypto' element={<Buycrypto />} />
            </Route>

            <Route index path='/dashboard/notification' element={<Notification />} />
            <Route index path='/dashboard/myaccount' element={<Myaccount />} />
            <Route index path='/dashboard/helpandsupport' element={<Helpandsupport />} />
            <Route index path='/dashboard/currencyexchange' element={<Currencyexchange />} />
            <Route index path='/dashboard/currencyexchange/summary' element={<Currencyexchangesummary />} />
            <Route index path='/dashboard/airtime-to-cash' element={<Airtimetocash />} />
            <Route index path='/dashboard/cash-request' element={<Cashrequest />} />
            <Route index path='/dashboard/cash-request/register' element={<Register />} />
            <Route index path='/ecommerce/dashboard' element={<Register />} />
            <Route index path='/ecommerce/dashboard' element={<Register />} />
          </Route>
        </Route>

      </Routes>
    </>
  )
}

export default App;