/* @import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap'); */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* input, in
put:focus{
  box-shadow: none;
  outline: none;
} */

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

@font-face {
  font-family: 'Geist Sans';
  src: url('/font/geist-sans-latin-100-normal.woff2') format('woff2');
  font-weight: 100;
  font-style: normal;
}

@font-face {
  font-family: 'Geist Sans';
  src: url('/font/geist-sans-latin-200-normal.woff2') format('woff2');
  font-weight: 200;
  font-style: normal;
}

@font-face {
  font-family: 'Geist Sans';
  src: url('/font/geist-sans-latin-300-normal.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Geist Sans';
  src: url('/font/geist-sans-latin-400-normal.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Geist Sans';
  src: url('/font/geist-sans-latin-500-normal.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Geist Sans';
  src: url('/font/geist-sans-latin-600-normal.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Geist Sans';
  src: url('/font/geist-sans-latin-700-normal.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
}

body {
  margin: 0;
  font-family: 'Geist Sans', sans-serif;
}

:root {
  scroll-behavior: smooth;
}

* {
  box-sizing: border-box;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.btn_opacity {
  @apply opacity-70 cursor-not-allowed
}

.ant-dropdown-menu {
  max-width: 200px;
}

.react-international-phone-country-selector-button {
  height: 42px;
  padding: 1rem;
}


.loader {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: .5;
  }
}

.transition-opacity {
  transition-property: opacity;
}

.duration-300 {
  transition-duration: 300ms;
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

/* Add this to your existing CSS */
.ant-select-selection-placeholder {
  font-size: 12px !important;
}

.ant-select {
  border-radius: 20px;
  border: none;
}

/* If you want to target only the specific selects in the FilterTransaction component, 
   you can add a custom class to those selects and target that class */
/* .filter-select .ant-select-selection-placeholder {
  font-size: 12px !important;
} */