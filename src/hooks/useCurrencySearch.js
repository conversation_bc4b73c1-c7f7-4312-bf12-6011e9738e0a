import { useState, useCallback, useEffect } from 'react';
import { useInfiniteQuery } from '@tanstack/react-query';
import debounce from 'lodash.debounce';
import { getAllCurrencies } from '../../apis/currency';

const useCurrencySearch = (initialSearchTerm = '', additionalParams = {}) => {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [isSearching, setIsSearching] = useState(false);

  const {
    data: allCurrencyInfiniteData,
    fetchNextPage,
    hasNextPage,
    isFetching: isCurrencyFetching,
    isFetchingNextPage: isFetchingNextCurrencyPage,
  } = useInfiniteQuery({
    queryKey: ["getallcurrencies", searchTerm, additionalParams],
    queryFn: ({ pageParam = 1 }) =>
      getAllCurrencies({
        // is_crypto: false,
        name: searchTerm,
        page: pageParam,
        ...additionalParams,
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.next) {
        const url = new URL(lastPage.next);
        const page = url.searchParams.get('page');
        return page ? parseInt(page, 10) : undefined;
      }
      return undefined;
    },
    initialPageParam: 1,
  });

  const debouncedSearch = useCallback(
    debounce((value) => {
      setSearchTerm(value); // Update the search term
      setIsSearching(false);
    }, 300),
    []
  );

  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const handleScroll = (e) => {
    const { target } = e;
    const { scrollTop, scrollHeight, clientHeight } = target;
    const isNearBottom = scrollHeight - scrollTop <= clientHeight * 1.5;

    if (isNearBottom && hasNextPage && !isFetchingNextCurrencyPage) {
      fetchNextPage();
    }
  };

  return {
    searchTerm,
    setSearchTerm,
    isSearching,
    setIsSearching,
    allCurrencyInfiniteData,
    isCurrencyFetching,
    isFetchingNextCurrencyPage,
    debouncedSearch,
    handleScroll,
  };
};

export default useCurrencySearch;