import { useState } from 'react';

const useModal = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [content, setContent] = useState(null);
  const [proceed, setProceed] = useState('');
  const [title, setTitle] = useState('');
  const [onProceed, setOnProceed] = useState(()=> () => {});


  const openModal = (modalTitle, modalContent, proceedText, onProceedCallback) => {
    setTitle(modalTitle);
    setContent(modalContent);
    setProceed(proceedText)
    setOnProceed(()=>onProceedCallback)
    setIsOpen(true);
  };

  const closeModal = () => {
    setIsOpen(false);
    setContent(null); 
    setTitle('');
    setProceed('')
    setOnProceed(()=> () => {})
  };

  return {
    isOpen,
    content,
    title,
    proceed,
    onProceed,
    openModal,
    closeModal,
  };
};

export default useModal;
