import { useState } from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";

const loginWithGoogle = (onSuccessCallback) => {
  const [userDataFromGoogle, setUserDataFromGoogle] = useState(null);
  const navigate = useNavigate();

  const responseMessage = async (response) => {
    try {
      const { access_token } = response;
      localStorage.setItem('googleAccessToken', access_token);
      setUserDataFromGoogle(response);

      // Call the callback function with the token
      if (onSuccessCallback) {
        onSuccessCallback(access_token);
        // Let the callback handle navigation and success messages
        return;
      }

      // Don't navigate here - let the login mutation handle it
      toast.success('Successfully logged in with Google', { duration: 5000 });
    } catch (error) {
      console.error('Error logging in with Google:', error);
      toast.error('Failed to log in with Google', { duration: 5000 });
    }
  };

  const errorMessage = (error) => {
    console.error('Google login error:', error);
    toast.error('Google login failed');
  };

  return {
    responseMessage,
    errorMessage,
    userDataFromGoogle,
  };
};

export default loginWithGoogle;
