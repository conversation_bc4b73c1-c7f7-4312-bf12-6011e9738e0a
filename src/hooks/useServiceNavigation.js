import { useNavigate, useLocation } from "react-router-dom";
import { useCallModal } from "./useCallModal";

export const useServiceNavigation = () => {
  const { isOpenModal, openModal, closeModal } = useCallModal();
  const navigate = useNavigate();
  const location = useLocation();

  const items = [
    { label: "send money", path: "/dashboard/sendmoney" },
    { label: "bill payment", path: "/dashboard/billpayment" },
    { label: "airtime to cash", path: "/dashboard/airtime-to-cash" },
    { label: "swap", path: "/dashboard/currencyexchange" },
    { label: "card", path: "/dashboard/cards" },
  ];

  const handleServiceClick = async (service) => {
    if (service?.status === "suspended" || service?.status === "coming soon") {
      return;
    }

    if (service?.verification_required) {
      navigate(`/dashboard/services-verification/${service.slug}`);
      return;
    }

    if (service?.name === "receive money") {
      openModal();
    } else {
      const matchedItem = items.find(
        (item) => item.label.toLowerCase() === service?.name.toLowerCase()
      );
      if (matchedItem) {
        navigate(matchedItem.path);
      }
    }
  };

  const isServiceSuspendedOrComingSoon = (service) =>
    service?.status === "suspended" || service?.status === "coming soon";

  return {
    handleServiceClick,
    isServiceSuspendedOrComingSoon,
    isOpenModal,
    closeModal
  };
};