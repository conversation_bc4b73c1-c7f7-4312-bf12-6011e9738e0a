import React, { forwardRef } from 'react';

const Dropdown = forwardRef(({ isOpen, onClose, menuItems, className }, ref) => {
  if (!isOpen) return null;

  return (
    <div ref={ref} className={`absolute right-2 -mt-4 w-72 bg-white border border-border_color rounded-lg shadow-lg z-10 ${className}`}>
      <div className="p-2">
        {menuItems.map((item, index) => (
          <button
            key={index}
            className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
            onClick={() => {
              item.onClick();
              onClose();
            }}
          >
            {item.label}
          </button>
        ))}
      </div>
    </div>
  );
});

Dropdown.displayName = 'Dropdown';

export default Dropdown;
