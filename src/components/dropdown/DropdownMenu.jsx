import React from 'react';

const DropdownMenu = ({ onMarkAsRead, onDelete, closeDropdown }) => {
  return (
    // <div className='.relative .w-full' onClick={closeDropdown}>
      <div className="absolute bg-white shadow-lg rounded-lg border border-border_color w-40 overflow-hidden">
        <button
          onClick={() => {
            onMarkAsRead();
            closeDropdown();
          }}
          className="block w-full font-[500] text-left px-4 py-2 text-xs text-gray-700 hover:bg-gray-100"
        >
          Mark as Read
        </button>
        <button
          onClick={() => {
            onDelete();
            closeDropdown();
          }}
          className="block w-full font-[500] text-left px-4 py-2 text-xs hover:text-white hover:bg-red-600"
        >
          Delete Notification
        </button>
      </div>
    // </div>

  );
};

export default DropdownMenu;
