import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Button from './button/button';
import { BsInfoCircle } from "react-icons/bs";

const NotificationBar = () => {
  const navigate = useNavigate();

  return (
    <div className='flex items-center justify-center'>
      <div className="relative bg-blue-50 border border-blue-200 w-full rounded-md py-4">
        <div className="flex items-center justify-between px-4">
          <div className="text-sm flex items-center gap-2">
            <BsInfoCircle />
            Please complete your account verification to ensure account security
          </div>

          <div>
            <Button
              onClick={() => navigate('/dashboard/myaccount?tab=verifications')}
              className="p-3 px-6 text-xs"
            >
              Complete Verification
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationBar;
