import React, { useEffect } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getAllServices } from '../../apis/settings';

const ServiceVerificationGuard = () => {
  const location = useLocation();
  
  // Fetch all services
  const { data: services, isLoading } = useQuery({
    queryKey: ["getallservices"],
    queryFn: getAllServices
  });
  
  // If services are still loading, render the outlet
  if (isLoading || !services?.results) {
    return <Outlet />;
  }
  
  // Extract the service path from the URL
  const path = location.pathname;
  const match = path.match(/\/dashboard\/([^/]+)/);
  const servicePath = match ? match[1] : null;
  
  // Skip verification check for these paths
  if (!servicePath || 
      servicePath === 'dashboard' || 
      servicePath === 'welcome' || 
      servicePath === 'services-verification' || 
      servicePath === 'myaccount' ||
      servicePath === 'wallet' ||
      servicePath === 'notification' ||
      servicePath === 'helpandsupport' ||
      servicePath === 'transactions') {
    return <Outlet />;
  }
  
  // Map service paths to service names
  const serviceMap = {
    'sendmoney': 'send money',
    'billpayment': 'bill payment',
    'airtime-to-cash': 'airtime to cash',
    'currencyexchange': 'swap',
    'cards': 'card',
    'sendcrypto': 'send crypto',
    'buycrypto': 'buy crypto'
  };
  
  // Get the service name from the path
  const serviceName = serviceMap[servicePath];
  
  // Find the service that matches the current route
  const currentService = services.results.find(
    service => service.name.toLowerCase() === serviceName || 
               service.children?.some(child => child.name.toLowerCase() === serviceName)
  );
  
  // If service requires verification and we're not already on the verification page
  if (currentService?.verification_required && !path.includes('services-verification')) {
    return <Navigate to={`/dashboard/services-verification/${currentService.slug}`} state={{ from: location }} replace />;
  }
  
  return <Outlet />;
};

export default ServiceVerificationGuard;