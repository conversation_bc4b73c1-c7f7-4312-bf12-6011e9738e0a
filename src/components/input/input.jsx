import React from 'react'

const Input = ({defaultValue, placeholder, value, name, className, onChange, type="text", id, ...props}) => {
  return (
    <input 
      id={id}
      type={type}
      placeholder={placeholder}
      value={value}
      name={name}
      onChange={onChange}
      defaultValue={defaultValue}
      className={`border rounded-[8px] text-[#404850] text-[14px] flex items-center w-full focus:outline-none ${className}`}
      {...props}
    />
  )
}

export default Input