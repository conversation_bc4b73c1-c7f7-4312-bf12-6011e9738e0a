import React from 'react'

const Button = ({ children, onClick, variant = 'primary', disabled = false, type = 'button', className, loading }) => {

  const getButtonStyles = () => {
    switch (variant) {
      case 'primary':
        return 'bg-auth_bg text-white duration-500 hover:bg-blue-700';
      case 'secondary':
        return 'bg-secondary_bg text-white hover:bg-black hover:bg-opacity-50';
      case 'danger':
        return 'bg-red-500 text-white hover:bg-red-700';
      case 'transparent':
        return 'bg-transparent border text-blue-800';
      default:
        return 'bg-auth_bg text-white hover:bg-blue-700';
    }
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`whitespace-nowrap rounded-[8px] font-[400] w-full .text-text_auth ${getButtonStyles()} ${className} ${disabled || loading ? 'btn_opacity' : ''
        }`}
    >
      {loading ? "loading..." : children}
    </button>
  )
}

export default Button