import { createPortal } from "react-dom"
import "./modal.css"

const Modal = ({
  children,
  className,
  styles,
  width = "w-[398px]",
  position,
  onClose,
  showCloseButton = true,
  title = "",
}) => {
  return (
    <>
      {createPortal(
        <div className="overflow-hidden">
          <div className="overlay"></div>
          <div className={`fixed z-30 ${position} ${width} ${styles || ""} overflow-y-hidden bg-white rounded-[10px]`}>
            <div className={`${className ?? ""} ${position ? "rounded-[8px]" : "rounded-2xl"} w-full py-[16px] px-[24px]`}>
              {/* {title && */}
              <div className="mb-[24px]">
                <div className="flex items-center justify-between">
                  {title && (
                    <p className="text-[16px] font-md text-auth_bg">{title}</p>
                  )}
                  {showCloseButton && (
                    <div className={!title && `flex justify-end w-full`}>
                      <button onClick={onClose} className="bg-gray-200 hover:bg-blue-200 transition-all duration-300 p-1 rounded-full flex items-center justify-center">
                        <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  )}
                </div>
              </div>
              {/* } */}
              {/* {showCloseButton && !title &&
                <div className="flex justify-end ">
                  <button onClick={onClose} className="">
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              } */}
              <div className="max-h-[70vh] overflow-auto -mx-[24px] px-[24px]">
                {children}
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}
    </>
  )
}

export default Modal;