// components/Modal.js

import React from 'react';
import Button from '../button/button';
import { ButtonLoader } from '../../utils/ButtonLoader';

const Modal = ({ isOpen, onClose, title, children, proceed, isProceed = false, onProceed, className, showCloseButton=true, disabled=false }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-100 flex items-center justify-center bg-black bg-opacity-80" onClick={onClose}>
      <div className="bg-white z-100 rounded-xl w-11/12 md:w-1/3" onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between items-center p-4 border-b">
          <div className={`flex items-center w-full ${className}`}>
            <h2 className={` text-[16px] font-[500] text-auth_bg ${className}`}>{title}</h2>
          </div>
          <button onClick={onClose} className="text-xl hover:text-auth_bg hover:bg-[#EDEFFF] rounded-full p-1 flex items-center justify-center w-6 h-6">&times;</button>
        </div>
        <div className={`p-4 text-center ${className}`}>
          {children}
        </div>
        <div className="flex justify-end py-8 px-20 .border-t flex-col gap-2">
          
          {isProceed && (
            <Button onClick={onProceed} disabled={disabled} className={`p-3 text-xs ${disabled ? 'cursor-not-allowed opacity-50' : ''}`}>{disabled ? <ButtonLoader /> : proceed}</Button>
          )}

          { showCloseButton && (
            <button onClick={onClose} className="py-3 bg-white text-black text-xs border w-full rounded-[8px] font-[500]">Close</button>
          )}

        </div>
      </div>
    </div>
  );
};

export default Modal;
