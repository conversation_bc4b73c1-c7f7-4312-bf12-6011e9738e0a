import { useQuery } from "@tanstack/react-query";
import Input from "../input/input";
import { getAllCards } from "../../../apis/card";
import Button from "../button/button";
import { HiMiniCheckCircle } from "react-icons/hi2";
import { PageLoader } from "../../utils/ButtonLoader";
import { GoCircle, GoPlus } from "react-icons/go";
import { EnterPin } from "./EnterPin";
import { useCallModal } from "../../hooks/useCallModal";
import { useState } from "react";
import { AddnewPhysicalCard } from "./AddnewPhysicalCard";


export const GetAllCards = ({ continuation, setCardId, isPending }) => {

  const [selectedCard, setSelectedCard] = useState(null)

  const { data: userCardsQuery, isPending: isFetchingUserCards } = useQuery({
    queryKey: ["getallusercards"],
    queryFn: () => getAllCards({ is_virtual: false })
  })

  const handleCardSelect = (card) => {
    setSelectedCard(selectedCard === card ? null : card)
    setCardId(card); // Pass the selected card ID to the parent component
  }

  const { openModal, isOpenModal, closeModal: closeEnterPinModal } = useCallModal()

  return (
    <>
      {isFetchingUserCards ? (
        <PageLoader />
      ) : (
        <div>
          <div className="rounded-xl border p-4 space-y-4">

            <AddnewPhysicalCard />

            {userCardsQuery?.results?.map((item) => {
              const { last_4_digits, card_type, exp_month, exp_year, bank, id, image } = item;

              return (

                <div
                  key={id}
                  onClick={() => handleCardSelect(id)}
                  className={`${selectedCard === id ? "bg-gray-100 border-auth_bg" : ""} hover:bg-gray-100 transition-all duration-500 cursor-pointer border w-full rounded-xl p-5 flex items-center justify-between`}
                >
                  <div className="flex items-center gap-3 w-[70%]">
                    <img src={image ? image : "/images/logo/faviconblue.png"} className="w-[30px] h-[30px] rounded-full" />

                    <div className="flex flex-col justify-center gap-1 ">
                      <h5 className="capitalize text-xs text-black">
                        {card_type} ending with {last_4_digits}
                      </h5>
                      <p className="text-xs text-gray-500">Expiry {exp_month}/{exp_year}</p>
                    </div>
                  </div>
                  <div>
                    {selectedCard === id ? (
                      <HiMiniCheckCircle className="text-auth_bg" />
                    ) : (
                      <GoCircle />
                    )}
                  </div>
                </div>
              )
            })}

          </div>

          <div className="mt-6">
            <Button
              type='Submit'
              disabled={!selectedCard}
              onClick={() => openModal()}
              className={"p-3 px-6 btn flex items-center justify-center gap-2 font-md text-xs"}
            >
              <span>Proceed</span>
            </Button>
          </div>
        </div>
      )}

      {(isOpenModal && selectedCard) && (
        <EnterPin closeModal={closeEnterPinModal} continuation={continuation} isPending={isPending} />
      )}
    </>
  )
}