import { useState } from "react";
import Modal from "../modal/Modal";
import { GetAllCards } from "./GetAllCards";
import { IoWalletOutline } from "react-icons/io5";
import { TbCreditCard } from "react-icons/tb";
import { GetAllWallets } from "./GetAllWallets";

export const PaymentMethod = ({
  walletBal,
  closeModal,
  continuation,
  isPending,
  setPaymentMethod,
  setCardId,
}) => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("wallet");

  const handlePaymentMethodSelect = (method) => {
    setSelectedPaymentMethod(method);
    setPaymentMethod(method); // Update payment method in parent component
  };

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[450px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Payment Method"
      >
        <div className="flex flex-col .h-[80vh] overflow-hidden">
          {/* Fixed Payment Method Selector */}
          <div className="fixed top-12 left-0 right-0 bg-white z-10 py-4">
            <div className="flex items-center justify-center">
              <div className="flex items-center rounded-full bg-[#F2F4F7] p-2 w-fit font-medium">
                <div
                  onClick={() => handlePaymentMethodSelect("wallet")}
                  className={`${
                    selectedPaymentMethod === "wallet" &&
                    "bg-white rounded-full text-auth_bg"
                  } cursor-pointer p-3 px-8 text-xs flex items-center gap-2`}
                >
                  <IoWalletOutline size={20} />{" "}
                  <span className="text-sm">Wallet</span>
                </div>

                <div
                  onClick={() => handlePaymentMethodSelect("card")}
                  className={`${
                    selectedPaymentMethod === "card" &&
                    "bg-white rounded-full text-auth_bg"
                  } cursor-pointer p-3 px-8 text-xs flex items-center gap-2`}
                >
                  <TbCreditCard size={20} />{" "}
                  <span className="text-sm">Card</span>
                </div>
              </div>
            </div>

            <div className="my-4">
              <hr />
            </div>
          </div>

          {/* Scrollable Content Section */}
          <div className="flex-1 overflow-y-auto mt-28"> {/* Adjust margin-top to account for the fixed section */}
            {selectedPaymentMethod === "wallet" && (
              <GetAllWallets
                walletBal={walletBal}
                continuation={continuation}
                isPending={isPending}
              />
            )}

            {selectedPaymentMethod === "card" && (
              <GetAllCards
                continuation={continuation}
                setCardId={setCardId}
                isPending={isPending}
              />
            )}
          </div>
        </div>
      </Modal>
    </>
  );
};