import React, { useEffect, useState } from 'react'
import Modal from '../modal/Modal';
import PinInput from 'react-pin-input';
import Button from '../button/button';
import { ButtonLoader } from '../../utils/ButtonLoader';
import { TfiControlForward } from "react-icons/tfi"

export const EnterPin = ({ closeModal, continuation, isPending }) => {
  const [transactionPin, setTransactionPin] = useState("");

  const handleInputChange = (value) => {
    setTransactionPin(value);
  };

  const onProceed = async () => {
    try {
      await continuation(transactionPin);
      // closeModal(); // Close modal on success
    } catch (error) {
      console.error("Error:", error);
    }
  };

  useEffect(() => {
    if (transactionPin.length === 4) {
      continuation(transactionPin);
    }
  }, [transactionPin]);

  return (
    <>
      <Modal
        width="w-[90%] sm:max-w-[400px]"
        position="modal-center"
        onClose={closeModal}
        showCloseButton={true}
        title="Enter Transaction Pin"
      >
        <div className="overflow-hidden">
          <div className="px-10">
            <div className="overflow-hidden">
              <p className="text-sm text-center mb-6 text-general_gray_text">Please enter your 4 digit PIN</p>

              <div className='mb-[16px] text-center'>
                <PinInput
                  length={4}
                  initialValue=""
                  secret={false}
                  secretDelay={100}
                  onChange={handleInputChange}
                  type="numeric"
                  inputMode="number"
                  style={{ width: "100%" }}
                  inputStyle={{ background: "#E7E7E7", borderRadius: "3px", border: "none", margin: "4px" }}
                  inputFocusStyle={{ border: "1px solid #335ADD" }}
                  onComplete={(value, index) => { }}
                  autoSelect={true}
                  regexCriteria={/^[ A-Za-z0-9_@./#&+-]*$/}
                />
              </div>

              <div className="mt-6">
                <Button
                  type="submit"
                  disabled={transactionPin.length !== 4 || isPending}
                  onClick={onProceed}
                  className={`p-3 px-6 btn flex items-center justify-center gap-2 font-md text-xs ${(transactionPin.length !== 4 || isPending) && 'btn-opacity'
                    }`}
                >
                  {isPending ? (
                    <ButtonLoader />
                  ) : (
                    <>
                      <span>Proceed</span>
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};