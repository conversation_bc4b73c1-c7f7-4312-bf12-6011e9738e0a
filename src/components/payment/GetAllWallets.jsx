import { useQuery } from "@tanstack/react-query";
import { HiMiniCheckCircle } from "react-icons/hi2";
import { getWallet } from "../../../apis/wallet";
import Button from "../button/button";
import { PageLoader } from "../../utils/ButtonLoader";
import { GoCircle } from "react-icons/go";
import { useState } from "react";
import { EnterPin } from "./EnterPin";
import { useCallModal } from "../../hooks/useCallModal";

export const GetAllWallets = ({ walletBal, continuation, isPending }) => {

  console.log("wal", walletBal)
  const [selectedWallet, setSelectedWallet] = useState(null)

  const { data: userFiatWallet, isPending:isFetchingUserWallet } = useQuery({
    queryKey: ["getselectedwallet"],
    queryFn: () => getWallet({ currency_code: walletBal?.code })
  })

  const handleWalletSelect = (wallet) => {
    setSelectedWallet(selectedWallet === wallet ? null : wallet)
  }

  const { openModal, isOpenModal, closeModal: closeEnterPinModal } = useCallModal()

  console.log("sw", selectedWallet)

  return (
    <>
      {isFetchingUserWallet ? (
        <PageLoader />
      ) : (

        <div>
          <div className="rounded-xl border p-4 space-y-4 mb-20">
            <p className="text-xs font-normal">Wallet balances</p>

            {userFiatWallet?.results?.map((item) => (
              <div
                key={item?.id}
                onClick={() => handleWalletSelect(item?.currency?.code)}
                className={`${selectedWallet === item?.currency?.code ? "bg-gray-100 border-auth_bg" : ""} hover:bg-gray-100 transition-all duration-500 cursor-pointer border w-full rounded-xl p-5 flex items-center justify-between`}
              >
                <div className="flex items-center gap-3 w-[70%]">
                  <img src={item?.currency?.image ? item?.currency?.image : "/images/logo/faviconblue.png"} className="w-[30px] h-[30px] rounded-full" />

                  <div className="flex flex-col justify-center gap-1 ">
                    <h5 className="text-xs text-black">
                      {item?.currency?.symbol}{parseFloat(item?.balance).toLocaleString()} <span className="text-gray-500">{item?.currency?.code}</span>
                    </h5>
                    <p className="text-xs text-gray-500">Pay with {item?.currency?.code}</p>
                  </div>
                </div>
                <div>
                  {selectedWallet === item?.currency?.code ? (
                    <HiMiniCheckCircle className="text-auth_bg" />
                  ) : (
                    <GoCircle />
                  )}
                </div>
              </div>
            ))}

          </div>

          <div className="fixed bottom-0 left-0 right-0 bg-white z-10 py-4 p-10">
            <Button
              type='Submit'
              disabled={!selectedWallet}
              onClick={() => openModal()}
              className={"p-3 px-6 btn flex items-center justify-center gap-2 font-md text-xs"}
            >
              <span>Proceed</span>
            </Button>
          </div>
        </div>
      )}

      {(isOpenModal && selectedWallet) && (
        <EnterPin closeModal={closeEnterPinModal} continuation={continuation} isPending={isPending}/>
      )}
    </>
  )
}