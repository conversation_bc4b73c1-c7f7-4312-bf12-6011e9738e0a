import { useMutation } from "@tanstack/react-query";
import { addCardApi } from "../../../apis/card";
import { GoPlus } from "react-icons/go";
import { PageLoader } from "../../utils/ButtonLoader";

export const AddnewPhysicalCard = () => {

  let domain = window.location.host
  let protocol = window.location.protocol
  const callBackUrl = `${protocol}//${domain}/dashboard/cards?tab=physical`


  const apiPayload = {
    redirect_url: callBackUrl,
    is_virtual: false,
    source: "web"
  };

  const { mutateAsync: addCardMutation, isPending: addCardPending } = useMutation({
    mutationKey: ["add-new-card"],
    mutationFn: addCardApi,
  });

  const handleProceed = async () => {

    try {
      // Call the API to add the card
      const response = await addCardMutation(apiPayload);
      if (response && response.payment_link) {
        window.location.href = response.payment_link
      }
    } catch (error) {
      console.error('Error adding card:', error);
    }
  };

  return (
    <div>

      <div
        onClick={handleProceed}
        className={`hover:bg-gray-100 transition-all duration-500 cursor-pointer border w-full rounded-xl p-5 flex items-center justify-between`}
      >
        {addCardPending ? (
          <div className="w-full flex items-center justify-center">
          <PageLoader />
          </div>
        ) : (
          <div className="flex items-center gap-4">
            <div className="flex items-center justify-center p-2 bg-gray-100 rounded-md">
              <GoPlus size={20} className="text-auth_bg" />
            </div>

            <div className="flex flex-col justify-center gap-1 ">
              <h5 className="text-xs text-black">
                Add debit card
              </h5>
              <p className="text-xs text-gray-500">Connect your debit for seamless transaction </p>
            </div>
          </div>
        )}
      </div>

    </div>
  )
}