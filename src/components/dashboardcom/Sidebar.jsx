import React from 'react'
import { RiWallet3Fill } from "react-icons/ri";
import { GiWallet } from "react-icons/gi";
import { Link, useLocation } from 'react-router-dom';
import { RiMenuAddLine } from "react-icons/ri";
import { IoCardSharp } from "react-icons/io5";
import { RiExchangeFill } from "react-icons/ri";
import { IoWalletSharp } from "react-icons/io5";
import { HiMiniWallet } from "react-icons/hi2";
import { PiCirclesFourFill } from "react-icons/pi";


const links = [
  {
    name: "Dashboard",
    href: "/ecommerce/dashboard",
    icon: <PiCirclesFourFill size={18} />,
  },
  {
    name: "Order",
    href: "/ecommerce/order",
    icon: <RiWallet3Fill size={18} />,
  },
  {
    name: "Payment",
    href: "/ecommerce/payment",
    icon: <IoWalletSharp size={18} />,
  },
  {
    name: "product",
    href: "/ecommerce/product",
    icon: <HiMiniWallet size={18} />,
  },
  {
    name: "Stores",
    href: "/ecommerce/stores",
    icon: <RiExchangeFill size={18} />,
  },
  {
    name: "Customer Feedback",
    href: "/ecommerce/customer-feedback",
    icon: <GiWallet size={18} />,
  },
  {
    name: "Account Summary",
    href: "/ecommerce/account-summary",
    icon: <IoCardSharp size={18} />,
  },
  {
    name: "Help and Support",
    href: "/ecommerce/help-and-support",
    icon: <RiMenuAddLine size={18} />,
  },

]

const Sidebar = ({ closeSidebar }) => {
  const { pathname } = useLocation();

  return (
    <div >
      <div className="relative sidebar bg-auth_bg min-h-screen inline-flex flex-col justify-between p-6 text-text_auth overflow-x-hidden">
        <div className='w-[220px]'>

          <div className='flex items-center justify-center'>
            <div className='relative h-[40px] w-[170px] mb-10 mt-6'>
              <img src="/images/logo/logowhite.png" className='w-full h-full object-contain' alt="Auth Image" />
            </div>
          </div>

          <div className='pl-4 my-4 text-sm font-[400]'>MENU</div>

          <div>
            <div className='my-2 font-[400]'>

              {links.map((item) => (
                <Link key={item.name} to={item.href} onClick={closeSidebar} className={`pl-4 py-[10px] cursor-pointer flex items-center space-x-3 my-3 ${pathname === item?.href && 'bg-white text-auth_bg rounded-[4px]'} hover:bg-white hover:text-auth_bg hover:rounded-[4px] focus:bg-white focus:text-auth_bg focus:rounded-[4px]`}>
                  {item.icon}
                  <span className='text-xs'>{item.name}</span>
                </Link>
              ))}

            </div>
          </div>
          {/* <hr className='absolute left-0 right-0 w-full h-1' />
          <div className='mt-10'>
            <div className='flex items-center justify-between'>
              <div>
                <div>Logout</div>
                <div className='text-xs'>Segun Isreal A.</div>
              </div>
              <div className='p-2 flex items-center justify-center bg-[#0107384D] rounded-full'>
                <HiOutlineLogout size={20} />
              </div>
            </div>
          </div> */}
        </div>
      </div>
    </div>
  )
}

export default Sidebar