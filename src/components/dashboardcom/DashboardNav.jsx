import React, { useEffect, useState } from 'react'
import { IoChevronDown } from "react-icons/io5";
import { IoIosNotifications } from "react-icons/io";
import Dropdown from '../dropdown/dropdown';
import { useQuery } from '@tanstack/react-query';
import { IoChevronForwardOutline } from 'react-icons/io5';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import useDropdown from '../../hooks/useDropdown';
import { getAllNotification } from '../../../apis/notification';
import Breadcrumbs from '../../utils/Breadcrumbs';
import { Headercontent } from '../../constant/Constant';
import Allmenus from './Allmenus';
import toast from 'react-hot-toast';
import { SlUser } from "react-icons/sl";




const DashboardNav = () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  // console.log("myroute", router.push)

  const { isDropdownOpen, handleCloseDropdown, handleToggleDropdown, dropdownRef } = useDropdown();

  const handleLogout = () => {
    // e.preventDefault();
    localStorage.clear()
    toast.success("You have successfully logout")
    // Pass state to indicate this is a logout navigation
    navigate("/", { state: { from: 'logout' } })
  }

  const menuItems = [
    { label: 'My Account', onClick: () => { navigate('/dashboard/myaccount'); } },
    { label: 'Notification', onClick: () => { navigate('/dashboard/notification'); } },
    // { label: 'Company', onClick: () => console.log('Settings clicked') },
    { label: 'Logout', onClick: () => handleLogout() }
  ];


  const [state, setState] = useState("")

  useEffect(() => {
    const localStorageCredential = typeof window !== 'undefined' && JSON.parse(localStorage.getItem("Kompat_userData"));

    if (localStorageCredential) {
      setState(localStorageCredential?.userData || "");
    }

  }, [])

  const { data: allNotificationQuery } = useQuery({ queryKey: ["getallnotification"], queryFn: getAllNotification })


  return (
    <>
      <header className="fixed top-0 left-0 right-0 bg-white shadow-sm z-20">
        <div className='flex items-center md:justify-between justify-between p-2 pe-10 md:px-14 border-b'>
          <div className='flex gap-6'>
            <div className='relative w-[80px]'>
              <div className="absolue left-0">
                <Link to="/dashboard">
                  <img src="/images/logo/logoblue.png" className='w-full h-full object-contain' alt="Auth Image" />
                </Link>
              </div>
            </div>
          </div>

          <ul className="hidden md:flex items-center justify-between md:gap-10 text-general_gray_text">
            {Headercontent.map((item, index) => (
              <li key={index} className="relative group">
                <Link to={item.path} className={`hover:text-auth_bg flex items-center gap-1 text-[12px] font-medium ${pathname === item.path && 'text-auth_bg'}`}>
                  {item.icon} <span>{item.name}</span>
                </Link>

                {item.dropdown && (
                  <ul className="absolute left-0 hidden group-hover:block bg-white shadow-md rounded-md text-sm w-40">
                    {item.dropdown.map((dropdownItem, dropdownIndex) => (
                      <li key={dropdownIndex}>
                        <Link
                          to={dropdownItem.path}
                          className="block px-4 py-2 hover:text-auth_bg text-xs"
                        >
                          {dropdownItem.name}
                        </Link>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>

          <div className='flex items-center gap-x-4'>
            <Allmenus />

            <Link to={"/dashboard/notification"}>
              <div className='relative'>
                <div className='w-10 h-10 rounded-full flex items-center justify-center bg-[#F5F5F5]'>
                  <IoIosNotifications size={20} />
                </div>
                <div className='absolute right-0 top-0 flex items-center justify-center bg-red-600 rounded-full h-4 w-4 text-white'>
                  <span className='text-[8px] font-medium'>{allNotificationQuery?.count}</span>
                </div>
              </div>
            </Link>

            <div className='flex items-center gap-2 cursor-pointer border rounded-full p-1' onClick={handleToggleDropdown} ref={dropdownRef}>
              <div className='relative h-10 w-10 rounded-full overflow-hidden'>
                {state?.profile_picture === null ? (
                  <div className='w-full h-full flex items-center justify-center bg-gray-200'>
                    <SlUser />
                  </div>
                ) : (
                  <img src={state?.profile_picture} alt='img' className='object-cover w-full h-full' />
                )}
              </div>
              <div className='hidden md:block'>
                <div className='text-xs'>{state?.first_name + " " + state?.last_name} </div>
                <div className='text-xs text-general_gray_text'>Hi, Welcome Back</div>
              </div>
              <div>
                <IoChevronDown />
              </div>
            </div>
          </div>
        </div>
        <Dropdown
          isOpen={isDropdownOpen}
          onClose={handleCloseDropdown}
          menuItems={menuItems}
          className=''
          ref={dropdownRef}
        />
      </header>
      {/* Add a spacer div to prevent content from hiding behind the fixed header */}
      <div className="h-[72px]"></div>
    </>
  )
}

export default DashboardNav;