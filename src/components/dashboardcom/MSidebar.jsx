import { Link, useLocation } from "react-router-dom";
import { Headercontent } from "../../constant/Constant";

export const MSidebar = ({ isOpen, closeMenu }) => {

  const { pathname } = useLocation();


  return (

    <div>
      <div onClick={(e) => closeMenu() && e.stopPropagation()} className={`fixed inset-0 z-40 flex ${isOpen ? 'translate-x-0' : 'translate-x-full'} transition-transform duration-300 ease-in-out sm:hidden`}>
        <div className="flex-1 bg-black opacity-40"></div>
        <div className="w-[70%] bg-white shadow-md">
          <ul className="flex flex-col leading-10 pl-4 pt-10 md:gap-10 text-general_gray_text">
            {Headercontent.map((item, index) => (
              <li key={index} className="relative group">
                <Link to={item.path} className={`hover:text-auth_bg flex items-center gap-1 text-[12px] font-semibold ${pathname === item.path && 'text-auth_bg font-[500]'}`}>
                  {item.icon} <span>{item.name}</span>
                </Link>

                {/* Render dropdown if it exists */}
                {item.dropdown && (
                  <ul className="absolute left-0 hidden group-hover:block bg-white shadow-md rounded-md text-sm w-40">
                    {item.dropdown.map((dropdownItem, dropdownIndex) => (
                      <li key={dropdownIndex}>
                        <Link
                          to={dropdownItem.path}
                          className="block px-4 py-2 hover:text-lightblue text-xs"
                        >
                          {dropdownItem.name}
                        </Link>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  )
};