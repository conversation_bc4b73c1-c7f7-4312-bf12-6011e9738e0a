import React, { useState, useRef, useEffect } from 'react'
import { IoGrid } from 'react-icons/io5'
import { useQuery } from '@tanstack/react-query';
import { getAllServices } from '../../../apis/settings';
import { useServiceNavigation } from '../../hooks/useServiceNavigation';
import { Receivemoneycurrencytype } from '../../pages/dashboard/receivemoney/components/receivemoneycurrencytype';
import { PageLoader } from '../../utils/ButtonLoader';
import useServiceStore from '../../store/useServiceStore';

const Allmenus = () => {
  const { setServices, setLoading, setError, setSelectedService } = useServiceStore();
  const { data: getAllServicesQuery, isPending } = useQuery({
    queryKey: ["getallservices"],
    queryFn: getAllServices,
    onSuccess: (data) => {
      setServices(data);
      setLoading(false);
    },
    onError: (error) => {
      setError(error);
      setLoading(false);
    },
  });

  const { handleServiceClick, isServiceSuspendedOrComingSoon, isOpenModal, closeModal } = useServiceNavigation();

  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Handle mouse enter and leave for the entire dropdown
  const handleMouseEnter = () => {
    setIsOpen(true);
  };

  const handleMouseLeave = () => {
    setIsOpen(false);
  };

  // Store the selected service in the store when clicked
  const handleServiceSelection = (service) => {
    setSelectedService(service);
    handleServiceClick(service);
    setIsOpen(false);
  };

  return (
    <div 
      className="relative" 
      ref={dropdownRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="flex flex-col items-center gap-1 cursor-pointer">
        <div className="w-8 h-8 rounded-full p-2 flex items-center justify-center text-white bg-auth_bg">
          <IoGrid size={20} />
        </div>
        <p className='hidden md:flex text-xs font-medium text-general_gray_text'>Services</p>
      </div>

      {/* Add invisible bridge to maintain hover */}
      {isOpen && (
        <>
          <div className="absolute top-full left-0 h-2 w-full" />
          <div className="absolute top-full left-0 -translate-x-20 sm:-translate-x-32 mt-2 bg-white shadow-lg rounded-xl p-2 sm:p-4 z-50 w-[200px] sm:w-[300px]">
            {isPending ? (
              <div>
                <PageLoader />
              </div>
            ) : (
              <div className='grid grid-cols-3 gap-2 gap-y-6'>
                {getAllServicesQuery?.results?.map((item, index) => {
                  const isSuspendedOrComingSoon = isServiceSuspendedOrComingSoon(item);
                  return (
                    <div
                      className={`${isSuspendedOrComingSoon && 'btn_opacity opacity-50'} flex flex-col items-center hover:scale-110 transition-transform cursor-pointer`}
                      key={index}
                      onClick={() => handleServiceSelection(item)}
                    >
                      <div className="flex h-10 w-10 items-center justify-center p-2 rounded-md bg-[#DFE3FF]">
                        <img src={!item.icon ? '/images/logo/faviconblue.png' : item.icon} className="w-[30px] h-[25px]" />
                      </div>
                      <div className='mt-2 text-[8px] font-medium text-auth_bg text-center capitalize'>{item.name}</div>
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        </>
      )}

      {isOpenModal && (
        <div>
          <Receivemoneycurrencytype closeModal={closeModal} />
        </div>
      )}
    </div>
  );
};

export default Allmenus;
