import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const Tabparams = ({ defaultTab }) => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Extract the 'tab' query parameter from the URL
  const getQueryParam = (key) => {
    const params = new URLSearchParams(location.search);
    return params.get(key);
  };

  const initialTab = getQueryParam('tab') || defaultTab;
  const [switchInfo, setSwitchInfo] = useState(initialTab);

  const handleSwitch = (type) => {
    setSwitchInfo(type);
    navigate(`?tab=${type}`);
  };

  useEffect(() => {
    const currentTab = getQueryParam('tab');
    if (currentTab) {
      setSwitchInfo(currentTab);
    }
  }, [location.search]); // Depend on location.search to re-run the effect when the URL changes

  return { handleSwitch, switchInfo };
};

export default Tabparams;
