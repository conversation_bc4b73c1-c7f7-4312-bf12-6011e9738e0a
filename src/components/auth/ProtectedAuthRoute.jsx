import { Navigate, Outlet } from 'react-router-dom';

// This component prevents authenticated users from accessing auth pages
const ProtectedAuthRoute = () => {
  // Simple check for authentication based on localStorage
  const isAuthenticated = () => {
    try {
      // Check if we have both token and user data
      const hasToken = localStorage.getItem('token') !== null;
      const hasUserData = localStorage.getItem('Kompat_userData') !== null;
      
      return hasToken && hasUserData;
    } catch (error) {
      // If there's any error reading localStorage, consider user not authenticated
      return false;
    }
  };

  // If user is authenticated, redirect to dashboard
  // Otherwise, render the auth routes (Outlet)
  return isAuthenticated() ? <Navigate to="/dashboard" replace /> : <Outlet />;
};

export default ProtectedAuthRoute;