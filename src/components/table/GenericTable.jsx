import React, { useState, useEffect } from 'react';
import { IoChevronDown, IoChevronUp, IoChevronForward, IoChevronBack } from 'react-icons/io5';
import { PageLoader } from '../../utils/ButtonLoader';
import { FaSortDown, FaSortUp } from "react-icons/fa6";


const GenericTable = ({ 
  columns, 
  data, 
  isPending, 
  emptyMessage = "No data available", 
  itemsPerPage = 5,
  onRowClick
}) => {
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [currentPage, setCurrentPage] = useState(1);
  const [sortedData, setSortedData] = useState([]);
  const [rowsPerPage, setRowsPerPage] = useState(itemsPerPage);
  
  // Handle sorting
  const requestSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Apply sorting to data
  useEffect(() => {
    if (!data?.results) return;
    
    let sortableItems = [...data.results];
    if (sortConfig.key !== null) {
      sortableItems.sort((a, b) => {
        const aValue = getNestedValue(a, sortConfig.key);
        const bValue = getNestedValue(b, sortConfig.key);
        
        if (aValue === null || aValue === undefined) return 1;
        if (bValue === null || bValue === undefined) return -1;
        
        if (typeof aValue === 'string') {
          if (sortConfig.direction === 'asc') {
            return aValue.localeCompare(bValue);
          } else {
            return bValue.localeCompare(aValue);
          }
        } else {
          if (sortConfig.direction === 'asc') {
            return aValue > bValue ? 1 : -1;
          } else {
            return aValue < bValue ? 1 : -1;
          }
        }
      });
    }
    setSortedData(sortableItems);
  }, [data, sortConfig]);

  // Reset to first page when rows per page changes
  useEffect(() => {
    setCurrentPage(1);
  }, [rowsPerPage]);

  // Get nested object values using dot notation (e.g., "user.name")
  const getNestedValue = (obj, path) => {
    if (!path) return obj;
    const keys = path.split('.');
    return keys.reduce((o, key) => (o && o[key] !== undefined) ? o[key] : null, obj);
  };

  // Pagination logic
  const totalItems = data?.count || 0;
  const totalPages = Math.ceil(totalItems / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = Math.min(startIndex + rowsPerPage, sortedData.length);
  const currentItems = sortedData.slice(startIndex, endIndex);

  // Handle rows per page change
  const handleRowsPerPageChange = (e) => {
    setRowsPerPage(Number(e.target.value));
  };

  return (
    <div className="overflow-x-auto mt-6 no-scrollbar text-dashboard_sidebar_color">
      <table className="table-auto w-full">
        <thead className="bg-gray-100 border-b border-t">
          <tr className='capitalize py-4 text-[12px]'>
            {columns.map((column) => (
              <th 
                key={column.key} 
                className="first:px-4 last:px-4 py-3 text-left font-semibold text-gray-600 whitespace-nowrap cursor-pointer"
                onClick={() => column.sortable !== false && requestSort(column.key)}
              >
                <div className="flex items-center gap-1 uppercase text-[10px]">
                  {column.header}
                  {column.sortable !== false && (
                    <div className="flex flex-col ml-1">
                      <FaSortUp
                        size={10} 
                        className={sortConfig.key === column.key && sortConfig.direction === 'asc' ? 'text-auth_bg' : 'text-gray-400'} 
                      />
                      <FaSortDown
                        size={10} 
                        className={sortConfig.key === column.key && sortConfig.direction === 'desc' ? 'text-auth_bg' : 'text-gray-400'} 
                      />
                    </div>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {isPending && (
            <tr>
              <td colSpan={columns.length} className='text-xs pt-4 text-center'><PageLoader /></td>
            </tr>
          )}

          {!isPending && (!data?.count || data?.count <= 0) ? (
            <tr>
              <td colSpan={columns.length} className='text-xs pt-4 text-center'>{emptyMessage}</td>
            </tr>
          ) : (
            currentItems.map((item, index) => (
              <tr 
                key={index} 
                className="hover:bg-gray-100/50 transition-all duration-300 border-b text-[13px] cursor-pointer group whitespace-nowrap"
                onClick={() => onRowClick && onRowClick(item)}
              >
                {columns.map((column) => (
                  <td key={column.key} className="first:p-4 last:p-4 py-4">
                    {column.render ? column.render(item) : getNestedValue(item, column.key)}
                  </td>
                ))}
              </tr>
            ))
          )}
        </tbody>
      </table>
      
      {/* Pagination */}
      {!isPending && data?.count > 0 && (
        <div className="flex flex-wrap justify-between items-center mt-4 text-sm p-2 px-4 w-full">
          <div className="text-gray-500 text-xs mb-2 sm:mb-0">
            Showing {startIndex + 1}-{Math.min(startIndex + rowsPerPage, totalItems)} of {totalItems} entries
          </div>
          <div className="flex items-center gap-4 flex-wrap">
            <div className="flex items-center gap-2 text-xs text-gray-500">
              Rows per page:
              <select 
                value={rowsPerPage} 
                onChange={handleRowsPerPageChange}
                className="border rounded p-1 text-xs"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
            
            <div className="flex items-center">
              <button 
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className={`p-1 rounded border ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-600 hover:bg-gray-100'}`}
                aria-label="Previous page"
              >
                <IoChevronBack size={16} />
              </button>
              
              <span className="mx-4 text-xs">{currentPage}</span>
              
              <button 
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages || totalPages === 0}
                className={`p-1 rounded border ${currentPage === totalPages || totalPages === 0 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-600 hover:bg-gray-100'}`}
                aria-label="Next page"
              >
                <IoChevronForward size={16} />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GenericTable;
