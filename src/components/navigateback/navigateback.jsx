import { useLocation, useNavigate } from 'react-router-dom'
import { FaArrowLeft } from "react-icons/fa6";


const Navigateback = () => {

  const navigate = useNavigate()
  const location = useLocation();

  const shouldRouteBackToDashboard = () => {
    if (location.pathname === '/welcome' || '/dashboard/airtime-to-cash') {
      return true;
    }

    if (location.pathname.includes('/dashboard/transactions/')) {
      return true;
    }

    return false;
  };

  const handleNavigateBack = (e) => {
    e.preventDefault();

    if (shouldRouteBackToDashboard()) {
      // For welcome page and any transaction page, go back to dashboard
      navigate(-1);
    } else {
      navigate('/dashboard');
    }
  }



  return (
    <>
      <div className='flex items-center justify-start'>
        <div className='flex items-center gap-3 mb-5 text-auth_bg cursor-pointer' onClick={handleNavigateBack}>
          <FaArrowLeft />
          <span className='text-[13px]'>Go Back</span>
        </div>
      </div>
    </>
  )
}

export default Navigateback
