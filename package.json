{"name": "kompat-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-oauth/google": "^0.12.1", "@tanstack/react-query": "^5.52.1", "@tawk.to/tawk-messenger-react": "^2.0.2", "antd": "^5.20.2", "axios": "^1.7.4", "date-fns": "^3.6.0", "lodash.debounce": "^4.0.8", "react": "^18.3.1", "react-country-flag": "^3.1.0", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-infinite-scroll-component": "^6.1.0", "react-international-phone": "^4.3.0", "react-intersection-observer": "^9.16.0", "react-pin-input": "^1.3.1", "react-router-dom": "^6.26.1", "react-spinners": "^0.14.1", "react-to-print": "^3.1.0", "react-webcam": "^7.2.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.41", "tailwindcss": "^3.4.10", "vite": "^5.4.1"}}