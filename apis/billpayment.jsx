import { axiosInstance, handleError } from "./config";

export const allCategory = async (params) => {
  try {
    const response = await axiosInstance.get('/bill-payment/categories', { params });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getBillersCategoryWithId = async (categoryId) => {
  try {
    const response = await axiosInstance.get(`/bill-payment/categories/${categoryId}/billers`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getChildrenBiller = async (id) => {
  try {
    const response = await axiosInstance.get(`/bill-payment/billers/${id}/children`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getPackagesWithBillerId = async (billerId) => {
  try {
    const response = await axiosInstance.get(`/bill-payment/billers/${billerId}/packages`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const billpaymentValidateAccountNumber = async (payload) => {
  try {
    const response = await axiosInstance.post(`/bill-payment/billers/validate-account-number`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const billpaymentPayment = async (payload) => {
  try {
    const response = await axiosInstance.post(`/bill-payment/payment`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getListOfProvidersAirtimeToCash = async (billerId) => {
  try {
    const response = await axiosInstance.get(`/bill-payment/bill-payment-settings`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const requestAirtimeToCash = async (payload) => {
  try {
    const response = await axiosInstance.post(`/bill-payment/airtime-to-cash`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};