import axios from 'axios';
let url = import.meta.env.VITE_APP_KOMPAT_BASE_URL
// Create an Axios instance
export const axiosInstance = axios.create({
  baseURL: url,
  headers: { 'Content-Type': 'application/json' },
});

// Request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    // Check for token in localStorage
    const token = JSON.parse(localStorage.getItem('token'))?.userAuthToken?.access;
    if (token) config.headers.Authorization = `Bearer ${token}`;
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle unauthorized errors (token expired or invalid)
    if (error.response?.status === 401) {
      // Save current path for redirect after login
      const currentPath = window.location.pathname + window.location.search;
      localStorage.setItem('lastPath', currentPath);
      
      // Clear all auth-related data
      localStorage.removeItem('token');
      localStorage.removeItem('Kompat_userData');
      
      // Add state to indicate this is a forced logout
      window.location.href = "/?expired=true";
    }
    return Promise.reject(error);
  }
);

// Global error handler
export const handleError = (error) => {
  if (error?.response?.data) throw error.response.data;
  throw error;
};
