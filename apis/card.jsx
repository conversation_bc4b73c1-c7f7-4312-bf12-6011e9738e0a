import { axiosInstance, handleError } from "./config";

// account/cards

export const addCardApi = async (payload) => {
  try {
    const response = await axiosInstance.post('/card/', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getAllCards = async (params) => {
  try {
    const response = await axiosInstance.get('/card/', {params});
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const deleteCard = async ({id}) => {
  try {
    const response = await axiosInstance.delete(`/card/cards/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const FreezeCard = async ({id, ...payload}) => {
  try {
    const response = await axiosInstance.put(`/card/cards/${id}`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const cardConfig = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/card/config`, {params});
    return response.data;
  } catch (error) {
    handleError(error);
  }
};