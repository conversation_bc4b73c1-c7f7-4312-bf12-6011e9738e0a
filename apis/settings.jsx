import { axiosInstance, handleError } from "./config";

export const validateAccountNumber = async (payload) => {
  try {
    const response = await axiosInstance.post('/settings/banks/validate-account-number', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getAllServices = async () => {
  try {
    const response = await axiosInstance.get('/settings/services');
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getAllServicesVerification = async (id_or_slug, params) => {
  try {
    const response = await axiosInstance.get(`/settings/services/${id_or_slug}/verifications`, { params });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getAllServicesDetail = async () => {
  try {
    const response = await axiosInstance.get(`/settings/services/${id_or_slug}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getAllBanks = async () => {
  try {
    const response = await axiosInstance.get('/settings/banks/list');
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const mostUsedServices = async () => {
  try {
    const response = await axiosInstance.get('/settings/services/user-most-used');
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getAccountType = async () => {
  try {
    const response = await axiosInstance.get('/settings/account-type');
    return response.data;
  } catch (error) {
    handleError(error);
  }
};