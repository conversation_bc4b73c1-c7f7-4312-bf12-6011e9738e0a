import { axiosInstance, handleError } from "./config";

export const getAllNotification = async (page = 1) => {
  try {
    const response = await axiosInstance.get(`/notification/notifications`, {
      params: {
        page
      }
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const deleteNotification = async ({id}) => {
  try {
    const response = await axiosInstance.delete(`/notification/notifications/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const markNotificationAsRead = async ({id, ...payload}) => {
  try {
    const response = await axiosInstance.put(`/notification/notifications/${id}`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};