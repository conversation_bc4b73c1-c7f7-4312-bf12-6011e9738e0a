import { axiosInstance, handleError } from "./config";

export const getWallet = async (params) => {
  try {
    const response = await axiosInstance.get('/wallet/', {params});
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const createNewWallet = async (payload) => {
  try {
    const response = await axiosInstance.post('/wallet/', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const fundWallet = async (payload) => {
  try {
    const response = await axiosInstance.post('/wallet/fund-wallet', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getOrCreateWallet = async (currency_code, params) => {
  try {
    const response = await axiosInstance.get(`/wallet/get-or-create-wallet/${currency_code}`, {params});
    return response.data;
  } catch (error) {
    handleError(error);
  }
};