import { axiosInstance, handleError } from "./config";

// API request functions
export const kompatCreateAccountApi = async (payload) => {
  try {
    const response = await axiosInstance.post('/account/register', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getAccount = async () => {
  try {
    const response = await axiosInstance.get('/account/');
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const bankAccount = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/account/bank-accounts`, {
      params
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const loginApi = async (payload) => {
  try {
    const response = await axiosInstance.post('/account/login', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const resendVerificationCodeApi = async (payload) => {
  try {
    const response = await axiosInstance.post('/account/verification/resend-verification-code', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};


export const verifyAccountApi = async (payload) => {
  try {
    const response = await axiosInstance.post('/account/verification/verify-account', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};


export const resetPassword = async (payload) => {
  try {
    const response = await axiosInstance.post('/account/create-reset', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const verifyDocument = async (payload) => {
  try {
    // Check if payload is FormData (for file/image uploads)
    const isFormData = payload instanceof FormData;
    
    const response = await axiosInstance.post('/account/verification/verify-id', payload, 
      isFormData ? { headers: { 'Content-Type': 'multipart/form-data' } } : {}
    );
    return response.data;
  } catch (error) {
    handleError(error);
  }
};
    
export const completeReset = async (payload) => {
  try {
    const response = await axiosInstance.post('/account/complete-reset', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateAccount = async (payload) => {
  try {
    const response = await axiosInstance.put('/account/', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};


export const updateProfilePhoto = async (payload) => {
  try {
    const response = await axiosInstance.put('/account/upload-profile-picture', payload,
      { headers: { 'Content-Type': 'multipart/form-data' } }
    );
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const changeAccountType = async (payload) => {
  try {
    const response = await axiosInstance.post('/account/change-account-type', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};