import { axiosInstance, handleError } from "./config";

export const getAllCurrencies = async (params={}) => {
  try {
    const response = await axiosInstance.get("/currency/", { params });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const exchangeRate = async (payload) => {
  try {
    const response = await axiosInstance.post("/currency/exchange-rate", payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const currencycodeApi = async (currency_code) => {
  try {
    const response = await axiosInstance.get(`/currency/${currency_code}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};