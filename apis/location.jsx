import { axiosInstance, handleError } from "./config";

export const listOfCountries = async () => {
  try {
    const response = await axiosInstance.get("/location/countries");
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const listOfStates = async (country) => {
  try {
    const response = await axiosInstance.get(`/location/states?country=${country}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};