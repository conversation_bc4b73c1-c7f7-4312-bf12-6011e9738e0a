import { axiosInstance, handleError } from "./config";

export const getAllTransaction = async (params={}) => {
  try {
    // Format the params object to match the API's expected format
    const queryParams = { ...params };
    
    // Convert date_from to ISO format if present
    if (queryParams.date_from) {
      const date = new Date(queryParams.date_from);
      queryParams.date_from = date.toISOString().split('T')[0];
    }
    
    // Convert date_to to ISO format if present
    if (queryParams.date_to) {
      const date = new Date(queryParams.date_to);
      queryParams.date_to = date.toISOString().split('T')[0];
    }
    
    const response = await axiosInstance.get('/transaction/all', { params: queryParams });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const internalFundTransfer = async (payload) => {
  try {
    const response = await axiosInstance.post('/transaction/fund-transfer/internal', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const externalFundTransfer = async (payload) => {
  try {
    const response = await axiosInstance.post('/transaction/fund-transfer/external', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const currencyExchangeApi = async (payload) => {
  try {
    const response = await axiosInstance.post('/transaction/convert', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const fetchReceipt = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/transaction/fetch-receipt-data`, {params});
    return response.data;
  } catch (error) {
    handleError(error);
  }
};
