import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    // Disable sourcemaps in production to avoid the errors
    sourcemap: false,
    
    // Increase the warning limit for chunk sizes
    chunkSizeWarningLimit: 600,
    
    // Configure better code splitting
    rollupOptions: {
      output: {
        manualChunks: {
          // Split vendor chunks
          'vendor-react': ['react', 'react-dom', 'react-router-dom'],
          'vendor-query': ['@tanstack/react-query'],
          // Split UI libraries
          'ui-antd': ['antd'],
          'ui-icons': ['react-icons'],
        }
      }
    }
  }
})
